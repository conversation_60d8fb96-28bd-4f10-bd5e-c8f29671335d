@echo off
mkdir "C:\Newgema"

xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\run_gema.py" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\gema_agent.py" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\gema_config.py" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\requirements.txt" "C:\Newgema\"

xcopy /E /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\agent" "C:\Newgema\agent\"
xcopy /E /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\context_engine" "C:\Newgema\context_engine\"
xcopy /E /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\tools" "C:\Newgema\tools\"
xcopy /E /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\tests" "C:\Newgema\tests\"
xcopy /E /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\data" "C:\Newgema\data\"
xcopy /E /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\logs" "C:\Newgema\logs\"
xcopy /E /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\embeddings_cache" "C:\Newgema\embeddings_cache\"

xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\config.json" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\model_config.json" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\augment_auto_config.json" "C:\Newgema\"

xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\executar_agente.bat" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\run_gema.bat" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\run_gema_optimized.bat" "C:\Newgema\"

xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\cognitive_process.py" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\learning_system.py" "C:\Newgema\"
xcopy /E /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\llm_integration" "C:\Newgema\llm_integration\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\response_processor.py" "C:\Newgema\"

xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\code_formatter.py" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\line_break_fixer.py" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\encoding_tools.py" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\tool_executor.py" "C:\Newgema\"

xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\README_*.md" "C:\Newgema\"
xcopy /Y "C:\Gema_humano_vcode\Gema_humano_vcode\augment_agent_local2\comandos_exemplo.txt" "C:\Newgema\"

echo Estrutura copiada com sucesso para C:\Newgema!
pause