"""
Configuração para integração com modelos de linguagem.

Este módulo contém configurações para integração com diferentes
modelos de linguagem, incluindo modelos locais e remotos.
"""

import os
import json
from pathlib import Path

# Diretório base
BASE_DIR = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Caminho para o arquivo de configuração do modelo
MODEL_CONFIG_PATH = BASE_DIR / "model_config.json"

# Configuração padrão para o modelo
DEFAULT_MODEL_CONFIG = {
    "model_path": str(Path.home() / ".lmstudio/models/reedmayhew/claude-3.7-sonnet-reasoning-gemma3-12B/claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf"),
    "model_type": "claude",
    "context_size": 4096,
    "temperature": 0.7,
    "top_p": 0.9,
    "max_tokens": 2048
}

def load_model_config():
    """
    Carrega a configuração do modelo.

    Returns:
        dict: Configuração do modelo
    """
    try:
        if MODEL_CONFIG_PATH.exists():
            with open(MODEL_CONFIG_PATH, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"Configuração do modelo carregada de {MODEL_CONFIG_PATH}")
            return config
        else:
            print(f"Arquivo de configuração do modelo não encontrado: {MODEL_CONFIG_PATH}")
            print("Usando configuração padrão")
            return DEFAULT_MODEL_CONFIG
    except Exception as e:
        print(f"Erro ao carregar configuração do modelo: {e}")
        print("Usando configuração padrão")
        return DEFAULT_MODEL_CONFIG

# Carregar configuração do modelo
MODEL_CONFIG = load_model_config()

# Configuração para o modelo local
LOCAL_MODEL_CONFIG = {
    "model_path": MODEL_CONFIG.get("model_path", DEFAULT_MODEL_CONFIG["model_path"]),
    "model_type": MODEL_CONFIG.get("model_type", DEFAULT_MODEL_CONFIG["model_type"]),
    "context_size": MODEL_CONFIG.get("context_size", DEFAULT_MODEL_CONFIG["context_size"]),
    "temperature": MODEL_CONFIG.get("temperature", DEFAULT_MODEL_CONFIG["temperature"]),
    "top_p": MODEL_CONFIG.get("top_p", DEFAULT_MODEL_CONFIG["top_p"]),
    "max_tokens": MODEL_CONFIG.get("max_tokens", DEFAULT_MODEL_CONFIG["max_tokens"])
}

# Configuração para o modelo remoto
REMOTE_MODEL_CONFIG = {
    "api_url": "http://localhost:1234/v1",
    "api_key": "",
    "model": "deepseek-coder",
    "temperature": 0.7,
    "top_p": 0.9,
    "max_tokens": 2048
}

# Configuração para o modelo Gema
GEMA_MODEL_CONFIG = {
    "api_url": "https://api.gema.ai/v1",
    "api_key": os.environ.get("GEMA_API_KEY", ""),
    "model": "gema-7b-instruct",
    "temperature": 0.7,
    "top_p": 0.9,
    "max_tokens": 2048
}

# Tipo de modelo a ser usado (local, remote, gema)
MODEL_TYPE = "local"

# Obter configuração do modelo com base no tipo
def get_model_config(model_type=None):
    """
    Obtém a configuração do modelo com base no tipo.

    Args:
        model_type (str, optional): Tipo de modelo (local, remote, gema)

    Returns:
        dict: Configuração do modelo
    """
    model_type = model_type or MODEL_TYPE

    if model_type == "local":
        return LOCAL_MODEL_CONFIG
    elif model_type == "remote":
        return REMOTE_MODEL_CONFIG
    elif model_type == "gema":
        return GEMA_MODEL_CONFIG
    else:
        print(f"Tipo de modelo desconhecido: {model_type}")
        print("Usando modelo local")
        return LOCAL_MODEL_CONFIG
