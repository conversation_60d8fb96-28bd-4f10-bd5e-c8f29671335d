"""
Testes para o Modelo de Embedding

Este módulo contém testes para o modelo de embedding.
"""

import os
import sys
import unittest
import numpy as np
from pathlib import Path

# Adicionar diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from context_engine.embedding import EmbeddingModel
from utils.cache import MemoryCache

class TestEmbeddingModel(unittest.TestCase):
    """
    Testes para o modelo de embedding.
    """
    
    def setUp(self):
        """
        Configuração para os testes.
        """
        self.cache = MemoryCache(max_size=100)
        self.model = EmbeddingModel(
            embedding_dim=768,
            model_type="code",
            cache=self.cache
        )
        
    def test_embed_query(self):
        """
        Testa a geração de embedding para consultas.
        """
        # Gerar embedding
        query = "Como implementar um algoritmo de ordenação em Python?"
        embedding = self.model.embed_query(query)
        
        # Verificar dimensão
        self.assertEqual(embedding.shape, (768,))
        
        # Verificar normalização
        self.assertAlmostEqual(np.linalg.norm(embedding), 1.0, places=5)
        
    def test_embed_code(self):
        """
        Testa a geração de embedding para código.
        """
        # Gerar embedding
        code = """
        def bubble_sort(arr):
            n = len(arr)
            for i in range(n):
                for j in range(0, n - i - 1):
                    if arr[j] > arr[j + 1]:
                        arr[j], arr[j + 1] = arr[j + 1], arr[j]
            return arr
        """
        embedding = self.model.embed_code(code)
        
        # Verificar dimensão
        self.assertEqual(embedding.shape, (768,))
        
        # Verificar normalização
        self.assertAlmostEqual(np.linalg.norm(embedding), 1.0, places=5)
        
    def test_cache(self):
        """
        Testa o cache de embeddings.
        """
        # Gerar embedding
        query = "Como implementar um algoritmo de ordenação em Python?"
        embedding1 = self.model.embed_query(query)
        
        # Verificar estatísticas do cache
        self.assertEqual(self.model.stats['cache_misses'], 1)
        self.assertEqual(self.model.stats['cache_hits'], 0)
        
        # Gerar embedding novamente
        embedding2 = self.model.embed_query(query)
        
        # Verificar estatísticas do cache
        self.assertEqual(self.model.stats['cache_misses'], 1)
        self.assertEqual(self.model.stats['cache_hits'], 1)
        
        # Verificar que os embeddings são iguais
        np.testing.assert_array_equal(embedding1, embedding2)
        
    def test_detect_language(self):
        """
        Testa a detecção de linguagem.
        """
        # Python
        code = """
        def hello_world():
            print("Hello, world!")
        """
        self.assertEqual(self.model._detect_language(code), "python")
        
        # JavaScript
        code = """
        function helloWorld() {
            console.log("Hello, world!");
        }
        """
        self.assertEqual(self.model._detect_language(code), "javascript")
        
        # Java
        code = """
        public class HelloWorld {
            public static void main(String[] args) {
                System.out.println("Hello, world!");
            }
        }
        """
        self.assertEqual(self.model._detect_language(code), "java")
        
    def test_preprocess_code(self):
        """
        Testa o pré-processamento de código.
        """
        # Código com comentários e linhas em branco
        code = """
        # Este é um comentário muito longo que deve ser truncado durante o pré-processamento
        
        def hello_world():
            # Outro comentário
            print("Hello, world!")
            
            
        # Mais linhas em branco
        """
        
        processed_code = self.model._preprocess_code(code, "python")
        
        # Verificar que comentários longos foram truncados
        self.assertIn("# ...", processed_code)
        
        # Verificar que linhas em branco consecutivas foram removidas
        self.assertNotIn("\n\n\n", processed_code)
        
    def test_preprocess_query(self):
        """
        Testa o pré-processamento de consultas.
        """
        # Consulta com espaços extras
        query = "  Como implementar um algoritmo de ordenação em Python?  "
        
        processed_query = self.model._preprocess_query(query)
        
        # Verificar que espaços extras foram removidos
        self.assertEqual(processed_query, "Como implementar um algoritmo de ordenação em Python?")
        
if __name__ == "__main__":
    unittest.main()
