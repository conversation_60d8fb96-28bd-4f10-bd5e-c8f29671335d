"""
Fábrica de modelos LLM.

Este módulo fornece funções para criar instâncias de modelos LLM
com base na configuração.
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union

from .config import get_model_config, MODEL_TYPE
from .llm_interface import LLMInterface
from .local_llm import LocalLLM
from .gema_integration import GemaLLM

# Configurar logger
logger = logging.getLogger("augment.llm.factory")

def create_model(model_type: Optional[str] = None, config: Optional[Dict[str, Any]] = None) -> LLMInterface:
    """
    Cria uma instância de modelo LLM com base no tipo e configuração.
    
    Args:
        model_type: Tipo de modelo (local, remote, gema)
        config: Configuração personalizada
        
    Returns:
        Instância de modelo LLM
    """
    model_type = model_type or MODEL_TYPE
    config = config or get_model_config(model_type)
    
    logger.info(f"Criando modelo LLM do tipo: {model_type}")
    
    try:
        if model_type == "local":
            return create_local_model(config)
        elif model_type == "remote":
            return create_remote_model(config)
        elif model_type == "gema":
            return create_gema_model(config)
        else:
            logger.warning(f"Tipo de modelo desconhecido: {model_type}. Usando modelo local.")
            return create_local_model(config)
    except Exception as e:
        logger.error(f"Erro ao criar modelo: {e}")
        logger.warning("Usando modelo de fallback")
        return create_fallback_model()

def create_local_model(config: Dict[str, Any]) -> LocalLLM:
    """
    Cria uma instância de modelo local.
    
    Args:
        config: Configuração do modelo
        
    Returns:
        Instância de modelo local
    """
    model_path = config.get("model_path")
    
    if not model_path:
        raise ValueError("Caminho do modelo não especificado")
    
    # Verificar se o modelo existe
    if not os.path.exists(model_path):
        # Tentar encontrar o modelo em diretórios comuns
        common_dirs = [
            Path.home() / ".lmstudio/models",
            Path.home() / ".cache/lm-studio/models",
            Path.home() / "AppData/Local/lmstudio/models",
            Path.home() / ".local/share/lm-studio/models"
        ]
        
        for common_dir in common_dirs:
            # Procurar recursivamente por arquivos .gguf
            if common_dir.exists():
                for root, _, files in os.walk(common_dir):
                    for file in files:
                        if file.endswith(".gguf") and "deepseek" in file.lower():
                            model_path = os.path.join(root, file)
                            logger.info(f"Modelo encontrado: {model_path}")
                            break
                    
                    if model_path and os.path.exists(model_path):
                        break
        
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Modelo não encontrado: {model_path}")
    
    logger.info(f"Criando modelo local: {model_path}")
    
    return LocalLLM(model_path, config)

def create_remote_model(config: Dict[str, Any]) -> GemaLLM:
    """
    Cria uma instância de modelo remoto.
    
    Args:
        config: Configuração do modelo
        
    Returns:
        Instância de modelo remoto
    """
    api_url = config.get("api_url")
    api_key = config.get("api_key")
    model = config.get("model")
    
    if not api_url:
        raise ValueError("URL da API não especificada")
    
    logger.info(f"Criando modelo remoto: {api_url}")
    
    return GemaLLM(api_url, api_key, model, config)

def create_gema_model(config: Dict[str, Any]) -> GemaLLM:
    """
    Cria uma instância de modelo Gema.
    
    Args:
        config: Configuração do modelo
        
    Returns:
        Instância de modelo Gema
    """
    api_url = config.get("api_url")
    api_key = config.get("api_key")
    model = config.get("model")
    
    if not api_url:
        raise ValueError("URL da API não especificada")
    
    if not api_key:
        logger.warning("Chave de API não especificada")
    
    logger.info(f"Criando modelo Gema: {api_url}")
    
    return GemaLLM(api_url, api_key, model, config)

def create_fallback_model() -> LLMInterface:
    """
    Cria um modelo de fallback para quando ocorrem erros.
    
    Returns:
        Instância de modelo de fallback
    """
    logger.info("Criando modelo de fallback")
    
    # Usar GemaLLM em modo de simulação
    return GemaLLM(
        api_url="http://localhost:8000/v1",
        model="fallback",
        config={"simulation_mode": True}
    )
