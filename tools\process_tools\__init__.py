"""
Ferramentas de Processo

Este módulo implementa ferramentas para execução e gerenciamento de processos.
Inclui mecanismo de fallback para quando as dependências não estão disponíveis.

Complexidade:
- Tempo: Depende da implementação específica (O(1) para fallback, O(n) para implementação real)
- Espaço: O(1) para metadados, O(n) para resultados de processos
"""

import logging
import importlib.util

# Configurar logger
logger = logging.getLogger("tools.process")

# Verificar se psutil está disponível
psutil_available = importlib.util.find_spec("psutil") is not None

try:
    if psutil_available:
        # Importar implementação real
        from .process_tools import ProcessTools
        logger.info("Usando implementação completa das ferramentas de processo")
    else:
        # Importar implementação de fallback
        from .fallback_process import FallbackProcessTools as ProcessTools
        logger.warning("Usando implementação de fallback das ferramentas de processo")
except ImportError as e:
    # Em caso de erro, usar implementação de fallback
    logger.error(f"Erro ao importar ferramentas de processo: {e}")
    from .fallback_process import FallbackProcessTools as ProcessTools
    logger.warning("Usando implementação de fallback das ferramentas de processo devido a erro")

__all__ = ['ProcessTools']
