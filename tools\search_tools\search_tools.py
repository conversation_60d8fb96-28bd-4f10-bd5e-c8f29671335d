"""
Ferramentas de Busca

Este módulo implementa ferramentas para busca na web e em outras fontes.
"""

import os
import re
import logging
import json
import time
import requests
from typing import Dict, List, Any, Optional, Union, Tuple
from urllib.parse import urlparse, quote_plus
from bs4 import BeautifulSoup

from tools.tool_interface import Tool, ToolRegistry

# Configurar logger
logger = logging.getLogger("augment.tools.search")

class SearchTools:
    """
    Ferramentas para busca na web e em outras fontes.
    """
    
    def __init__(self, registry: ToolRegistry, config: Optional[Dict[str, Any]] = None):
        """
        Inicializa as ferramentas de busca.
        
        Args:
            registry: Registro de ferramentas
            config: Configuração das ferramentas (opcional)
        """
        self.registry = registry
        self.config = config or {}
        
        # Configurações
        self.google_api_key = self.config.get('google_api_key', os.environ.get('GOOGLE_API_KEY', ''))
        self.google_cx = self.config.get('google_cx', os.environ.get('GOOGLE_CX', ''))
        self.user_agent = self.config.get('user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        self.timeout = self.config.get('timeout', 10)
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_delay = self.config.get('retry_delay', 1)
        
        # Registrar ferramentas
        self._register_tools()
        
    def _register_tools(self):
        """
        Registra as ferramentas de busca.
        """
        # Ferramenta para busca na web
        self.registry.register(Tool(
            name="web_search",
            description="Busca informações na web",
            function=self.web_search,
            parameters={
                "query": {
                    "type": "string",
                    "description": "Consulta de busca",
                    "required": True
                },
                "num_results": {
                    "type": "integer",
                    "description": "Número de resultados",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para buscar documentação
        self.registry.register(Tool(
            name="search_documentation",
            description="Busca documentação de bibliotecas e frameworks",
            function=self.search_documentation,
            parameters={
                "query": {
                    "type": "string",
                    "description": "Consulta de busca",
                    "required": True
                },
                "library": {
                    "type": "string",
                    "description": "Biblioteca ou framework",
                    "required": True
                }
            }
        ))
        
        # Ferramenta para buscar código
        self.registry.register(Tool(
            name="search_code",
            description="Busca exemplos de código",
            function=self.search_code,
            parameters={
                "query": {
                    "type": "string",
                    "description": "Consulta de busca",
                    "required": True
                },
                "language": {
                    "type": "string",
                    "description": "Linguagem de programação",
                    "required": False
                },
                "num_results": {
                    "type": "integer",
                    "description": "Número de resultados",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para buscar Stack Overflow
        self.registry.register(Tool(
            name="search_stackoverflow",
            description="Busca perguntas e respostas no Stack Overflow",
            function=self.search_stackoverflow,
            parameters={
                "query": {
                    "type": "string",
                    "description": "Consulta de busca",
                    "required": True
                },
                "num_results": {
                    "type": "integer",
                    "description": "Número de resultados",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para buscar GitHub
        self.registry.register(Tool(
            name="search_github",
            description="Busca repositórios no GitHub",
            function=self.search_github,
            parameters={
                "query": {
                    "type": "string",
                    "description": "Consulta de busca",
                    "required": True
                },
                "num_results": {
                    "type": "integer",
                    "description": "Número de resultados",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para extrair conteúdo de página web
        self.registry.register(Tool(
            name="fetch_webpage",
            description="Extrai o conteúdo de uma página web",
            function=self.fetch_webpage,
            parameters={
                "url": {
                    "type": "string",
                    "description": "URL da página",
                    "required": True
                },
                "extract_text": {
                    "type": "boolean",
                    "description": "Se deve extrair apenas o texto",
                    "required": False
                }
            }
        ))
        
    def web_search(self, query: str, num_results: int = 5) -> List[Dict[str, str]]:
        """
        Busca informações na web.
        
        Args:
            query: Consulta de busca
            num_results: Número de resultados
            
        Returns:
            Lista de resultados
        """
        logger.info(f"Buscando na web: {query}")
        
        # Limitar número de resultados
        num_results = min(max(1, num_results), 10)
        
        # Tentar usar a API do Google se disponível
        if self.google_api_key and self.google_cx:
            return self._google_search(query, num_results)
            
        # Caso contrário, usar busca alternativa
        return self._fallback_search(query, num_results)
        
    def _google_search(self, query: str, num_results: int) -> List[Dict[str, str]]:
        """
        Busca usando a API do Google.
        
        Args:
            query: Consulta de busca
            num_results: Número de resultados
            
        Returns:
            Lista de resultados
        """
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                "key": self.google_api_key,
                "cx": self.google_cx,
                "q": query,
                "num": num_results
            }
            
            response = self._make_request("GET", url, params=params)
            
            if not response:
                return []
                
            results = []
            
            for item in response.get("items", []):
                results.append({
                    "title": item.get("title", ""),
                    "link": item.get("link", ""),
                    "snippet": item.get("snippet", "")
                })
                
            return results
        except Exception as e:
            logger.error(f"Erro na busca do Google: {e}")
            return []
            
    def _fallback_search(self, query: str, num_results: int) -> List[Dict[str, str]]:
        """
        Busca alternativa quando a API do Google não está disponível.
        
        Args:
            query: Consulta de busca
            num_results: Número de resultados
            
        Returns:
            Lista de resultados
        """
        try:
            # Usar DuckDuckGo como alternativa
            url = f"https://html.duckduckgo.com/html/?q={quote_plus(query)}"
            
            response = self._make_request("GET", url)
            
            if not response:
                return []
                
            soup = BeautifulSoup(response, "html.parser")
            results = []
            
            for result in soup.select(".result")[:num_results]:
                title_elem = result.select_one(".result__title")
                link_elem = result.select_one(".result__url")
                snippet_elem = result.select_one(".result__snippet")
                
                title = title_elem.get_text().strip() if title_elem else ""
                link = link_elem.get("href") if link_elem else ""
                snippet = snippet_elem.get_text().strip() if snippet_elem else ""
                
                results.append({
                    "title": title,
                    "link": link,
                    "snippet": snippet
                })
                
            return results
        except Exception as e:
            logger.error(f"Erro na busca alternativa: {e}")
            return []
            
    def search_documentation(self, query: str, library: str) -> List[Dict[str, str]]:
        """
        Busca documentação de bibliotecas e frameworks.
        
        Args:
            query: Consulta de busca
            library: Biblioteca ou framework
            
        Returns:
            Lista de resultados
        """
        logger.info(f"Buscando documentação: {query} (biblioteca: {library})")
        
        # Mapear bibliotecas para URLs de documentação
        doc_urls = {
            "python": "https://docs.python.org/3/",
            "numpy": "https://numpy.org/doc/stable/",
            "pandas": "https://pandas.pydata.org/docs/",
            "tensorflow": "https://www.tensorflow.org/api_docs/python/",
            "pytorch": "https://pytorch.org/docs/stable/",
            "django": "https://docs.djangoproject.com/en/stable/",
            "flask": "https://flask.palletsprojects.com/en/latest/",
            "react": "https://reactjs.org/docs/",
            "vue": "https://vuejs.org/guide/",
            "angular": "https://angular.io/docs",
            "node": "https://nodejs.org/en/docs/",
            "javascript": "https://developer.mozilla.org/en-US/docs/Web/JavaScript",
            "html": "https://developer.mozilla.org/en-US/docs/Web/HTML",
            "css": "https://developer.mozilla.org/en-US/docs/Web/CSS"
        }
        
        # Normalizar nome da biblioteca
        library = library.lower()
        
        # Verificar se a biblioteca é suportada
        if library not in doc_urls:
            return [{
                "title": f"Documentação para {library} não encontrada",
                "link": "",
                "snippet": f"A biblioteca {library} não está na lista de documentações suportadas."
            }]
            
        # Construir consulta específica para a biblioteca
        full_query = f"{query} site:{doc_urls[library]}"
        
        # Buscar na web
        return self.web_search(full_query, 5)
        
    def search_code(self, query: str, language: Optional[str] = None, num_results: int = 5) -> List[Dict[str, str]]:
        """
        Busca exemplos de código.
        
        Args:
            query: Consulta de busca
            language: Linguagem de programação
            num_results: Número de resultados
            
        Returns:
            Lista de resultados
        """
        logger.info(f"Buscando código: {query} (linguagem: {language})")
        
        # Construir consulta
        if language:
            full_query = f"{query} {language} code example site:github.com OR site:stackoverflow.com"
        else:
            full_query = f"{query} code example site:github.com OR site:stackoverflow.com"
            
        # Buscar na web
        return self.web_search(full_query, num_results)
        
    def search_stackoverflow(self, query: str, num_results: int = 5) -> List[Dict[str, str]]:
        """
        Busca perguntas e respostas no Stack Overflow.
        
        Args:
            query: Consulta de busca
            num_results: Número de resultados
            
        Returns:
            Lista de resultados
        """
        logger.info(f"Buscando no Stack Overflow: {query}")
        
        # Construir consulta
        full_query = f"{query} site:stackoverflow.com"
        
        # Buscar na web
        return self.web_search(full_query, num_results)
        
    def search_github(self, query: str, num_results: int = 5) -> List[Dict[str, str]]:
        """
        Busca repositórios no GitHub.
        
        Args:
            query: Consulta de busca
            num_results: Número de resultados
            
        Returns:
            Lista de resultados
        """
        logger.info(f"Buscando no GitHub: {query}")
        
        # Construir consulta
        full_query = f"{query} site:github.com"
        
        # Buscar na web
        return self.web_search(full_query, num_results)
        
    def fetch_webpage(self, url: str, extract_text: bool = False) -> str:
        """
        Extrai o conteúdo de uma página web.
        
        Args:
            url: URL da página
            extract_text: Se deve extrair apenas o texto
            
        Returns:
            Conteúdo da página
        """
        logger.info(f"Extraindo conteúdo da página: {url}")
        
        try:
            # Verificar URL
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                return f"URL inválida: {url}"
                
            # Fazer requisição
            response = self._make_request("GET", url)
            
            if not response:
                return f"Não foi possível acessar a página: {url}"
                
            # Extrair conteúdo
            if extract_text:
                soup = BeautifulSoup(response, "html.parser")
                
                # Remover scripts, estilos e tags ocultas
                for script in soup(["script", "style", "meta", "noscript", "header", "footer", "nav"]):
                    script.extract()
                    
                # Extrair texto
                text = soup.get_text(separator="\n")
                
                # Limpar texto
                lines = [line.strip() for line in text.splitlines() if line.strip()]
                text = "\n".join(lines)
                
                return text
            else:
                return response
        except Exception as e:
            logger.error(f"Erro ao extrair conteúdo da página: {e}")
            return f"Erro ao extrair conteúdo da página: {e}"
            
    def _make_request(self, method: str, url: str, **kwargs) -> Any:
        """
        Faz uma requisição HTTP com retry.
        
        Args:
            method: Método HTTP
            url: URL
            **kwargs: Argumentos adicionais para a requisição
            
        Returns:
            Resposta da requisição
        """
        headers = kwargs.pop("headers", {})
        headers["User-Agent"] = self.user_agent
        
        for attempt in range(self.max_retries):
            try:
                response = requests.request(
                    method,
                    url,
                    headers=headers,
                    timeout=self.timeout,
                    **kwargs
                )
                
                response.raise_for_status()
                
                # Verificar tipo de conteúdo
                content_type = response.headers.get("Content-Type", "")
                
                if "application/json" in content_type:
                    return response.json()
                else:
                    return response.text
            except requests.exceptions.RequestException as e:
                logger.warning(f"Erro na requisição (tentativa {attempt+1}/{self.max_retries}): {e}")
                
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    logger.error(f"Falha após {self.max_retries} tentativas: {e}")
                    return None
