@echo off
echo Iniciando Augment Agent Local...
echo.

REM Definir o diretório do script como o diretório atual
cd /d "%~dp0"

REM Verificar se o Python está instalado
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Erro: Python não encontrado. Por favor, instale o Python 3.8 ou superior.
    pause
    exit /b 1
)

REM Executar o script launcher
python main2.py %*

REM Pausar se houver erro
if %ERRORLEVEL% neq 0 (
    echo.
    echo Ocorreu um erro durante a execução. Verifique as mensagens acima.
    pause
)
