"""
Módulo de configuração para o Gema Agent estilo Augment.

Este módulo contém constantes, configurações e utilitários para
inicialização do Gema Agent.

Complexidade:
- Tempo: O(1) para a maioria das operações
- Espaço: O(1) para armazenamento de configurações
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Union

# Configurações padrão
DEFAULT_CONFIG = {
    # Configurações gerais
    "name": "Gema Agent (Estilo Augment)",
    "version": "1.0.0",
    "description": "Assistente de IA com estilo Augment para processamento de linguagem natural",
    
    # Configurações do modelo
    "model": {
        "name": "llama3",
        "context_size": 4096,
        "temperature": 0.7,
        "top_p": 0.9,
        "max_tokens": 2048
    },
    
    # Configurações do motor de contexto
    "context_engine": {
        "embedding_models": [
            "Salesforce/codet5p-220m",
            "microsoft/codebert-base"
        ],
        "chunk_size": 1024,
        "overlap": 128
    },
    
    # Configurações de ferramentas
    "tools": {
        "enabled": True,
        "default_encoding": "utf-8",
        "safe_mode": True
    },
    
    # Configurações de sistema
    "system": {
        "data_dir": "data",
        "logs_dir": "logs",
        "cache_dir": "cache",
        "max_history": 100
    },
    
    # Configurações de prompt
    "prompt": {
        "system_message": "Você é o Gema Agent, um assistente de IA com estilo Augment especializado em processamento de linguagem natural e execução de tarefas.",
        "tool_instructions": "Para usar uma ferramenta, você DEVE usar EXATAMENTE a sintaxe: {{tool.nome_da_ferramenta(arg1=valor1, arg2=valor2)}}",
        "examples": [
            "{{tool.write_file(path='exemplo.txt', content='Olá, mundo!')}}",
            "{{tool.read_file(path='exemplo.txt')}}"
        ]
    }
}

class GemaConfig:
    """
    Classe para gerenciar configurações do Gema Agent.
    
    Esta classe carrega, valida e fornece acesso às configurações
    do Gema Agent.
    
    Atributos:
        config (dict): Configurações carregadas
        config_path (Path): Caminho para o arquivo de configuração
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Inicializa o gerenciador de configurações.
        
        Args:
            config_path: Caminho para o arquivo de configuração (opcional)
        """
        self.config = DEFAULT_CONFIG.copy()
        self.config_path = Path(config_path) if config_path else Path("config.json")
        
        # Carregar configurações do arquivo, se existir
        self._load_config()
        
        # Configurar diretórios
        self._setup_directories()
        
        # Configurar logging
        self._setup_logging()
    
    def _load_config(self) -> None:
        """
        Carrega configurações do arquivo.
        
        Complexidade:
            Tempo: O(1) - operação de I/O
            Espaço: O(n) onde n é o tamanho do arquivo de configuração
        """
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                
                # Mesclar configurações carregadas com as padrão
                self._merge_configs(self.config, loaded_config)
                
                logging.info(f"Configurações carregadas de {self.config_path}")
            except Exception as e:
                logging.error(f"Erro ao carregar configurações: {e}")
    
    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> None:
        """
        Mescla configurações de forma recursiva.
        
        Args:
            base: Configurações base
            override: Configurações a serem mescladas
            
        Complexidade:
            Tempo: O(n) onde n é o número de chaves nas configurações
            Espaço: O(1) - operação in-place
        """
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_configs(base[key], value)
            else:
                base[key] = value
    
    def _setup_directories(self) -> None:
        """
        Configura diretórios necessários.
        
        Complexidade:
            Tempo: O(1) - operação de I/O
            Espaço: O(1) - operação in-place
        """
        # Obter diretórios das configurações
        data_dir = Path(self.config["system"]["data_dir"])
        logs_dir = Path(self.config["system"]["logs_dir"])
        cache_dir = Path(self.config["system"]["cache_dir"])
        
        # Criar diretórios se não existirem
        for directory in [data_dir, logs_dir, cache_dir]:
            directory.mkdir(exist_ok=True, parents=True)
    
    def _setup_logging(self) -> None:
        """
        Configura sistema de logging.
        
        Complexidade:
            Tempo: O(1) - operação de configuração
            Espaço: O(1) - operação in-place
        """
        logs_dir = Path(self.config["system"]["logs_dir"])
        log_file = logs_dir / "gema_agent.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def save(self) -> None:
        """
        Salva configurações no arquivo.
        
        Complexidade:
            Tempo: O(1) - operação de I/O
            Espaço: O(n) onde n é o tamanho das configurações
        """
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4)
            
            logging.info(f"Configurações salvas em {self.config_path}")
        except Exception as e:
            logging.error(f"Erro ao salvar configurações: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Obtém um valor de configuração.
        
        Args:
            key: Chave da configuração (pode ser aninhada com pontos)
            default: Valor padrão se a chave não existir
            
        Returns:
            Valor da configuração ou valor padrão
            
        Complexidade:
            Tempo: O(k) onde k é o número de níveis na chave
            Espaço: O(1) - operação de acesso
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """
        Define um valor de configuração.
        
        Args:
            key: Chave da configuração (pode ser aninhada com pontos)
            value: Valor a ser definido
            
        Complexidade:
            Tempo: O(k) onde k é o número de níveis na chave
            Espaço: O(1) - operação in-place
        """
        keys = key.split('.')
        config = self.config
        
        # Navegar até o penúltimo nível
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Definir o valor no último nível
        config[keys[-1]] = value
    
    def get_all(self) -> Dict[str, Any]:
        """
        Obtém todas as configurações.
        
        Returns:
            Dicionário com todas as configurações
            
        Complexidade:
            Tempo: O(1) - operação de acesso
            Espaço: O(1) - retorna referência
        """
        return self.config

# Instância global de configuração
config = GemaConfig()

# Teste simples
if __name__ == "__main__":
    # Imprimir configurações
    print("Configurações do Gema Agent:")
    print(json.dumps(config.get_all(), indent=4))
    
    # Testar acesso a configurações
    print(f"\nNome: {config.get('name')}")
    print(f"Modelo: {config.get('model.name')}")
    print(f"Tamanho do contexto: {config.get('model.context_size')}")
    
    # Testar definição de configurações
    config.set('model.temperature', 0.8)
    print(f"\nNova temperatura: {config.get('model.temperature')}")
    
    # Testar configuração inexistente
    print(f"\nConfiguração inexistente: {config.get('inexistente', 'valor padrão')}")
