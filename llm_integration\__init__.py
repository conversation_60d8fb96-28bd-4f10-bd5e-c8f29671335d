"""
Integração com LLMs

Este módulo implementa a integração com modelos de linguagem grandes (LLMs).
"""

from .llm_interface import LLMInterface
from .gema_integration import GemaLLM
from .local_llm import LocalLLM
from .config import get_model_config, MODEL_TYPE
from .model_factory import create_model

__all__ = [
    'LLMInterface',
    'GemaLLM',
    'LocalLLM',
    'get_model_config',
    'MODEL_TYPE',
    'create_model'
]

# Inicializar logging
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
