@echo off
setlocal enabledelayedexpansion
chcp 65001 > nul

REM ===================================================
REM Gema Agent (Estilo Augment) - Script de Execucao Otimizado
REM ===================================================

echo.
echo ===================================================
echo Gema Agent (Estilo Augment) - Versao Otimizada
echo ===================================================
echo.

REM Definir diretorio do script como diretorio atual
cd /d "%~dp0"

REM Verificar se o Python esta instalado
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Erro: Python nao encontrado. Por favor, instale o Python 3.8 ou superior.
    echo Voce pode baixar o Python em: https://www.python.org/downloads/
    echo.
    echo Pressione qualquer tecla para continuar...
    pause > nul
    exit /b 1
)

REM Verificar se as dependências estão instaladas
echo Verificando dependencias...
python -c "import chardet" 2>nul
if %ERRORLEVEL% neq 0 (
    echo Instalando dependencia: chardet
    pip install chardet
)

REM Verificar se o módulo line_break_fixer.py existe
if not exist "line_break_fixer.py" (
    echo Aviso: Arquivo line_break_fixer.py nao encontrado.
    echo Este arquivo e recomendado para o correto funcionamento do Gema Agent.
    echo.
    echo Deseja continuar mesmo assim? (S/N)
    set /p CONTINUE=
    if /i "!CONTINUE!" neq "S" (
        echo Operacao cancelada pelo usuario.
        echo.
        echo Pressione qualquer tecla para continuar...
        pause > nul
        exit /b 1
    )
)

REM Verificar argumentos
if "%~1"=="" (
    REM Sem argumentos, iniciar em modo interativo
    echo Iniciando Gema Agent em modo interativo...
    echo.
    echo Digite suas instrucoes em linguagem natural.
    echo Digite 'sair' para encerrar.
    echo.
    echo ===================================================
    echo.

    REM Executar com tratamento de erros
    python gema_augment_style.py --interactive
) else (
    REM Com argumentos, processar mensagem
    echo Processando mensagem: %*
    echo.
    echo ===================================================
    echo.

    REM Usar aspas para garantir que a mensagem seja tratada como um unico argumento
    python gema_augment_style.py --message "%*"
)

REM Verificar se houve erro
if %ERRORLEVEL% neq 0 (
    echo.
    echo Ocorreu um erro durante a execucao. Verifique as mensagens acima.
    echo.

    REM Verificar erros comuns
    echo Verificando informacoes do sistema...
    python -c "import sys; print('Versao do Python:', sys.version)" 2>nul
    python -c "import sys; print('Codificacao padrao:', sys.getdefaultencoding())" 2>nul
    python -c "import locale; print('Locale do sistema:', locale.getdefaultlocale())" 2>nul

    echo.
    echo Verificando modulos...
    python -c "import line_break_fixer; print('Modulo line_break_fixer disponivel')" 2>nul || echo "Modulo line_break_fixer NAO disponivel"
    python -c "import encoding_tools; print('Modulo encoding_tools disponivel')" 2>nul || echo "Modulo encoding_tools NAO disponivel"

    echo.
    echo Pressione qualquer tecla para continuar...
    pause > nul
) else (
    echo.
    echo Execucao concluida com sucesso!
    echo.
    echo Pressione qualquer tecla para continuar...
    pause > nul
)

exit /b %ERRORLEVEL%
