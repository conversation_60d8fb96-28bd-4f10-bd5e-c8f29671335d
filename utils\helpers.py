"""
Funções Auxiliares

Este módulo contém funções auxiliares para o Augment Agent.
"""

import os
import re
from typing import Optional, Dict, Any, List, Tuple

def detect_language(file_path: str) -> Optional[str]:
    """
    Detecta a linguagem de programação com base na extensão do arquivo.
    
    Args:
        file_path: Caminho do arquivo
        
    Returns:
        Linguagem detectada ou None se não for possível detectar
    """
    # Mapeamento de extensões para linguagens
    extension_map = {
        '.py': 'python',
        '.js': 'javascript',
        '.ts': 'typescript',
        '.jsx': 'javascript',
        '.tsx': 'typescript',
        '.java': 'java',
        '.c': 'c',
        '.cpp': 'cpp',
        '.h': 'cpp',
        '.hpp': 'cpp',
        '.cs': 'csharp',
        '.go': 'go',
        '.rb': 'ruby',
        '.php': 'php',
        '.swift': 'swift',
        '.kt': 'kotlin',
        '.rs': 'rust',
        '.scala': 'scala',
        '.html': 'html',
        '.css': 'css',
        '.scss': 'scss',
        '.sql': 'sql',
        '.sh': 'bash',
        '.ps1': 'powershell',
        '.md': 'markdown',
        '.json': 'json',
        '.xml': 'xml',
        '.yaml': 'yaml',
        '.yml': 'yaml'
    }
    
    # Obter extensão do arquivo
    _, ext = os.path.splitext(file_path.lower())
    
    # Retornar linguagem correspondente
    return extension_map.get(ext)

def format_code(code: str, language: Optional[str] = None) -> str:
    """
    Formata um snippet de código.
    
    Args:
        code: Código a ser formatado
        language: Linguagem do código (opcional)
        
    Returns:
        Código formatado
    """
    # Implementação básica - remover linhas em branco extras
    lines = code.split('\n')
    formatted_lines = []
    prev_empty = False
    
    for line in lines:
        is_empty = not line.strip()
        
        # Evitar linhas em branco consecutivas
        if is_empty and prev_empty:
            continue
            
        formatted_lines.append(line)
        prev_empty = is_empty
        
    # Remover espaços em branco no final das linhas
    formatted_lines = [line.rstrip() for line in formatted_lines]
    
    # Remover linhas em branco no início e no final
    while formatted_lines and not formatted_lines[0].strip():
        formatted_lines.pop(0)
        
    while formatted_lines and not formatted_lines[-1].strip():
        formatted_lines.pop()
        
    return '\n'.join(formatted_lines)

def parse_code(code: str, language: Optional[str] = None) -> Dict[str, Any]:
    """
    Analisa um snippet de código para extrair informações.
    
    Args:
        code: Código a ser analisado
        language: Linguagem do código (opcional)
        
    Returns:
        Informações extraídas do código
    """
    result = {
        'symbols': [],
        'imports': [],
        'classes': [],
        'functions': [],
        'language': language or 'unknown'
    }
    
    # Implementação específica por linguagem
    if language == 'python':
        # Extrair imports
        import_pattern = r'^(?:from\s+[\w.]+\s+)?import\s+[\w., \t]+'
        result['imports'] = [line.strip() for line in re.findall(import_pattern, code, re.MULTILINE)]
        
        # Extrair classes
        class_pattern = r'class\s+(\w+)(?:\([\w, ]*\))?:'
        result['classes'] = re.findall(class_pattern, code)
        
        # Extrair funções
        func_pattern = r'def\s+(\w+)\s*\('
        result['functions'] = re.findall(func_pattern, code)
        
        # Adicionar todos os símbolos
        result['symbols'] = result['classes'] + result['functions']
    elif language in ['javascript', 'typescript']:
        # Extrair imports
        import_pattern = r'(?:import|require)\s*\(?\s*[\w{}, .*\'"\s]+'
        result['imports'] = [line.strip() for line in re.findall(import_pattern, code, re.MULTILINE)]
        
        # Extrair classes
        class_pattern = r'class\s+(\w+)'
        result['classes'] = re.findall(class_pattern, code)
        
        # Extrair funções
        func_pattern = r'(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s*)?\([^)]*\)\s*=>|(\w+)\s*:\s*(?:async\s*)?\([^)]*\)\s*=>)'
        for match in re.finditer(func_pattern, code):
            func_name = next(filter(None, match.groups()), None)
            if func_name:
                result['functions'].append(func_name)
                
        # Adicionar todos os símbolos
        result['symbols'] = result['classes'] + result['functions']
    
    return result
