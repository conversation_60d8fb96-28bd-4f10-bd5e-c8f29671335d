"""
Sistema de Logging

Este módulo implementa um sistema de logging avançado para o Augment Agent.
"""

import os
import logging
import sys
from pathlib import Path
from typing import Optional, Dict, Any, Union
import datetime

class Logger:
    """
    Logger avançado com formatação personalizada e suporte a múltiplos destinos.
    """
    
    def __init__(self, name: str, log_level: str = "INFO", log_file: Optional[str] = None, 
                 console: bool = True, format_string: Optional[str] = None):
        """
        Inicializa o logger.
        
        Args:
            name: Nome do logger
            log_level: Nível de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_file: Caminho para o arquivo de log (opcional)
            console: Se deve logar no console
            format_string: String de formatação personalizada
        """
        self.name = name
        self.logger = logging.getLogger(name)
        
        # Converter string de nível para constante
        level_map = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL
        }
        
        log_level_value = level_map.get(log_level.upper(), logging.INFO)
        self.logger.setLevel(log_level_value)
        
        # Evitar duplicação de handlers
        self.logger.handlers = []
        
        # Formato padrão
        if format_string is None:
            format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            
        formatter = logging.Formatter(format_string)
        
        # Adicionar handler de console se solicitado
        if console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
            
        # Adicionar handler de arquivo se fornecido
        if log_file:
            # Garantir que o diretório exista
            log_dir = os.path.dirname(log_file)
            if log_dir:
                os.makedirs(log_dir, exist_ok=True)
                
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
            
    def debug(self, message: str, *args, **kwargs):
        """Loga uma mensagem de nível DEBUG."""
        self.logger.debug(message, *args, **kwargs)
        
    def info(self, message: str, *args, **kwargs):
        """Loga uma mensagem de nível INFO."""
        self.logger.info(message, *args, **kwargs)
        
    def warning(self, message: str, *args, **kwargs):
        """Loga uma mensagem de nível WARNING."""
        self.logger.warning(message, *args, **kwargs)
        
    def error(self, message: str, *args, **kwargs):
        """Loga uma mensagem de nível ERROR."""
        self.logger.error(message, *args, **kwargs)
        
    def critical(self, message: str, *args, **kwargs):
        """Loga uma mensagem de nível CRITICAL."""
        self.logger.critical(message, *args, **kwargs)
        
    def exception(self, message: str, *args, **kwargs):
        """Loga uma exceção com traceback."""
        self.logger.exception(message, *args, **kwargs)
        
    @staticmethod
    def setup_root_logger(log_level: str = "INFO", log_file: Optional[str] = None):
        """
        Configura o logger raiz.
        
        Args:
            log_level: Nível de log
            log_file: Caminho para o arquivo de log (opcional)
        """
        # Criar logger para o módulo atual
        return Logger("root", log_level, log_file)
        
    @staticmethod
    def get_default_log_file():
        """
        Obtém o caminho padrão para o arquivo de log.
        
        Returns:
            Caminho para o arquivo de log
        """
        # Usar diretório de logs na pasta do usuário
        home_dir = Path.home()
        log_dir = home_dir / "augment_logs"
        log_dir.mkdir(exist_ok=True)
        
        # Nome do arquivo baseado na data
        date_str = datetime.datetime.now().strftime("%Y-%m-%d")
        return str(log_dir / f"augment_{date_str}.log")
