"""
Implementação de fallback para ferramentas de busca.

Este módulo fornece implementações alternativas para as ferramentas de busca
quando as dependências necessárias não estão disponíveis.

Complexidade:
- Tempo: O(1) para a maioria das operações (simulações)
- Espaço: O(1) - dados estáticos
"""

import logging
import re
import json
from typing import Dict, List, Any, Optional, Union

# Configurar logger
logger = logging.getLogger("tools.search.fallback")

class FallbackSearchTools:
    """
    Implementação de fallback para ferramentas de busca.
    
    Esta classe fornece implementações simuladas das ferramentas de busca
    quando as dependências necessárias não estão disponíveis.
    """
    
    def __init__(self):
        """
        Inicializa as ferramentas de busca de fallback.
        """
        logger.warning("Usando implementação de fallback para ferramentas de busca")
        logger.warning("Funcionalidade limitada: instale 'beautifulsoup4' para funcionalidade completa")
        
        # Mensagem de fallback
        self.fallback_message = (
            "Não foi possível realizar a busca porque a biblioteca BeautifulSoup (bs4) "
            "não está instalada. Por favor, instale-a usando 'pip install beautifulsoup4'."
        )
        
        # Resultados simulados para testes
        self._simulated_results = {
            "python": [
                {
                    "title": "Python (linguagem de programação) - Wikipédia",
                    "url": "https://pt.wikipedia.org/wiki/Python",
                    "snippet": "Python é uma linguagem de programação de alto nível, interpretada, de script, imperativa, orientada a objetos, funcional, de tipagem dinâmica e forte."
                },
                {
                    "title": "Python.org",
                    "url": "https://www.python.org/",
                    "snippet": "Site oficial da linguagem Python, com documentação, downloads e recursos para a comunidade."
                }
            ],
            "augment": [
                {
                    "title": "Augment - Assistente de IA para Programação",
                    "url": "https://augment.dev/",
                    "snippet": "Augment é um assistente de IA para programação que ajuda desenvolvedores a escrever código mais rápido e com menos erros."
                }
            ]
        }
    
    def search(self, query: str, num_results: int = 5) -> List[Dict[str, str]]:
        """
        Simula uma busca na web.
        
        Args:
            query: Consulta de busca
            num_results: Número máximo de resultados
            
        Returns:
            Lista de resultados simulados
        """
        logger.warning(f"Busca simulada para: {query}")
        
        # Verificar se temos resultados simulados para esta consulta
        for key, results in self._simulated_results.items():
            if key.lower() in query.lower():
                logger.info(f"Retornando resultados simulados para: {key}")
                return results[:num_results]
        
        # Retornar resultado genérico
        return [
            {
                "title": f"Resultado simulado para: {query}",
                "url": "https://example.com/search",
                "snippet": self.fallback_message
            }
        ]
    
    def fetch_url(self, url: str) -> str:
        """
        Simula o download do conteúdo de uma URL.
        
        Args:
            url: URL para buscar
            
        Returns:
            Conteúdo simulado da página
        """
        logger.warning(f"Download simulado para: {url}")
        
        # Retornar conteúdo simulado
        return f"""
        <html>
            <head>
                <title>Página Simulada</title>
            </head>
            <body>
                <h1>Conteúdo Simulado</h1>
                <p>{self.fallback_message}</p>
                <p>URL solicitada: {url}</p>
            </body>
        </html>
        """
    
    def extract_text(self, html: str) -> str:
        """
        Simula a extração de texto de HTML.
        
        Args:
            html: Conteúdo HTML
            
        Returns:
            Texto extraído
        """
        logger.warning("Extração de texto simulada")
        
        # Tentar extrair texto com expressões regulares simples
        try:
            # Remover tags HTML de forma básica
            text = re.sub(r'<[^>]+>', ' ', html)
            # Remover espaços extras
            text = re.sub(r'\s+', ' ', text)
            # Remover espaços no início e fim
            text = text.strip()
            
            return text
        except Exception as e:
            logger.error(f"Erro ao extrair texto: {e}")
            return self.fallback_message
    
    def summarize(self, text: str, max_length: int = 200) -> str:
        """
        Simula a sumarização de texto.
        
        Args:
            text: Texto para sumarizar
            max_length: Comprimento máximo do resumo
            
        Returns:
            Resumo simulado
        """
        logger.warning("Sumarização simulada")
        
        # Truncar o texto para simular um resumo
        if len(text) > max_length:
            return text[:max_length] + "..."
        return text
