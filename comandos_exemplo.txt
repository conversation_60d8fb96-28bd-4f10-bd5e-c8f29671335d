# Arquivo de comandos de exemplo para o Gema Agent
# Linhas que começam com # são comentários e serão ignoradas

# Criar um arquivo de texto
Crie um arquivo chamado notas.txt com o conteúdo 'Estas são minhas notas importantes'

# Ler o conteúdo do arquivo
Leia o conteúdo do arquivo notas.txt

# Criar um script Python simples
Crie um arquivo Python chamado soma.py que some dois números fornecidos como argumentos de linha de comando

# Listar arquivos
Liste os arquivos no diretório atual

# Executar um comando do sistema
Execute o comando 'dir' para listar os arquivos no diretório atual
