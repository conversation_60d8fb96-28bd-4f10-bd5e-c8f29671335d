"""
Ferramentas de Codificação Universal para o Gema Agent

Este módulo fornece funções para lidar com diferentes codificações de caracteres,
garantindo que textos com caracteres especiais sejam manipulados corretamente.

Principais codificações suportadas:
- UTF-8: Padrão universal que pode representar qualquer caractere Unicode
- UTF-16: Codificação que usa 2 ou 4 bytes por caractere
- Latin-1 (ISO-8859-1): Codificação de 8 bits para línguas da Europa Ocidental
- Windows-1252: Codificação Windows similar ao Latin-1 com caracteres adicionais
- ASCII: Codificação básica de 7 bits (limitada a caracteres ingleses sem acentos)
"""

import os
import sys
import codecs
import unicodedata
import base64
from pathlib import Path

# Tentar importar chardet, mas continuar mesmo se não estiver disponível
try:
    import chardet
    CHARDET_AVAILABLE = True
except ImportError:
    CHARDET_AVAILABLE = False
    print("Aviso: Módulo 'chardet' não encontrado. A detecção automática de codificação será limitada.")

def detect_encoding(text_bytes):
    """
    Detecta a codificação de um texto em bytes.

    Args:
        text_bytes (bytes): Texto em bytes para detectar a codificação

    Returns:
        dict: Resultado da detecção com 'encoding', 'confidence' e outros dados
    """
    try:
        # Verificar se chardet está disponível
        if CHARDET_AVAILABLE:
            # Usar chardet para detectar a codificação
            result = chardet.detect(text_bytes)
            return result
        else:
            # Fallback se chardet não estiver disponível
            # Tentar algumas heurísticas simples

            # Verificar BOM para UTF-8, UTF-16 e UTF-32
            if text_bytes.startswith(codecs.BOM_UTF8):
                return {'encoding': 'utf-8', 'confidence': 0.9}
            elif text_bytes.startswith(codecs.BOM_UTF16_LE) or text_bytes.startswith(codecs.BOM_UTF16_BE):
                return {'encoding': 'utf-16', 'confidence': 0.9}
            elif text_bytes.startswith(codecs.BOM_UTF32_LE) or text_bytes.startswith(codecs.BOM_UTF32_BE):
                return {'encoding': 'utf-32', 'confidence': 0.9}

            # Tentar decodificar como UTF-8
            try:
                text_bytes.decode('utf-8')
                return {'encoding': 'utf-8', 'confidence': 0.8}
            except UnicodeDecodeError:
                pass

            # Tentar decodificar como latin-1 (sempre funciona, mas pode não ser correto)
            return {'encoding': 'latin-1', 'confidence': 0.5}
    except Exception as e:
        return {
            'encoding': 'utf-8',  # Padrão para UTF-8
            'confidence': 0.0,
            'error': str(e)
        }

def convert_encoding(text, source_encoding, target_encoding='utf-8'):
    """
    Converte um texto de uma codificação para outra.

    Args:
        text (str ou bytes): Texto para converter
        source_encoding (str): Codificação de origem
        target_encoding (str): Codificação de destino (padrão: utf-8)

    Returns:
        str: Texto convertido para a codificação de destino
    """
    try:
        # Se o texto já for bytes, decodificar primeiro
        if isinstance(text, bytes):
            text = text.decode(source_encoding, errors='replace')

        # Codificar para bytes na codificação de destino
        encoded_bytes = text.encode(target_encoding, errors='replace')

        # Decodificar de volta para string
        return encoded_bytes.decode(target_encoding)
    except Exception as e:
        return f"Erro na conversão de codificação: {str(e)}"

def normalize_unicode(text, form='NFC'):
    """
    Normaliza um texto Unicode para uma forma específica.

    Args:
        text (str): Texto para normalizar
        form (str): Forma de normalização (NFC, NFD, NFKC, NFKD)

    Returns:
        str: Texto normalizado
    """
    try:
        return unicodedata.normalize(form, text)
    except Exception as e:
        return f"Erro na normalização Unicode: {str(e)}"

def write_file_unicode(path, content, encoding='utf-8', bom=False):
    """
    Escreve conteúdo em um arquivo com suporte completo a Unicode.

    Esta implementação garante que as quebras de linha sejam preservadas corretamente
    em diferentes sistemas operacionais e que caracteres especiais sejam tratados adequadamente.

    Complexidade:
    - Tempo: O(n) onde n é o tamanho do conteúdo
    - Espaço: O(n) para armazenar o conteúdo em memória

    Args:
        path (str): Caminho do arquivo
        content (str): Conteúdo a ser escrito
        encoding (str): Codificação a ser usada (padrão: utf-8)
        bom (bool): Se True, adiciona BOM (Byte Order Mark) para UTF-8/16/32

    Returns:
        str: Mensagem de sucesso ou erro
    """
    try:
        # Verificar se o conteúdo é None
        if content is None:
            return f"Erro: Conteúdo não pode ser None"

        # Normalizar o caminho
        path = os.path.normpath(path)

        # Criar diretórios se necessário
        directory = os.path.dirname(path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

        # Determinar se deve usar BOM
        if bom and encoding.lower().startswith('utf'):
            mode = 'wb'  # Modo binário para adicionar BOM manualmente
        else:
            mode = 'w'

        # Processar o conteúdo para garantir quebras de linha corretas
        try:
            from line_break_handler import process_content_for_writing
            normalized_content = process_content_for_writing(content)
        except ImportError:
            # Fallback se o módulo não estiver disponível
            normalized_content = content.replace('\r\n', '\n').replace('\r', '\n')
            if '\\n' in normalized_content and not normalized_content.count('\n'):
                normalized_content = normalized_content.replace('\\n', '\n')

        # Escrever o arquivo
        with codecs.open(path, mode, encoding=encoding) as f:
            # Adicionar BOM se necessário
            if bom and mode == 'wb':
                if encoding.lower() == 'utf-8':
                    f.write(codecs.BOM_UTF8)
                elif encoding.lower() == 'utf-16':
                    f.write(codecs.BOM_UTF16)
                elif encoding.lower() == 'utf-32':
                    f.write(codecs.BOM_UTF32)

            # Escrever o conteúdo
            f.write(normalized_content)

        # Verificar se o arquivo foi escrito corretamente
        if os.path.exists(path):
            # Verificar o conteúdo do arquivo para garantir que foi escrito corretamente
            try:
                with open(path, 'r', encoding=encoding) as f:
                    file_content = f.read()

                # Verificar se o conteúdo foi escrito corretamente
                try:
                    from line_break_handler import verify_line_breaks
                    is_equal, is_content_equal, difference_description = verify_line_breaks(file_content, normalized_content)

                    if is_equal:
                        return f"Arquivo escrito com sucesso: {path} (codificação: {encoding})"
                    elif is_content_equal:
                        return f"Arquivo escrito com sucesso: {path} (codificação: {encoding}, quebras de linha normalizadas)"
                    else:
                        return f"Aviso: Arquivo escrito, mas {difference_description}: {path}"
                except ImportError:
                    # Fallback se o módulo não estiver disponível
                    file_content_norm = file_content.replace('\r\n', '\n').replace('\r', '\n')
                    content_norm = normalized_content.replace('\r\n', '\n').replace('\r', '\n')

                    if file_content_norm == content_norm:
                        return f"Arquivo escrito com sucesso: {path} (codificação: {encoding})"
                    else:
                        return f"Aviso: Arquivo escrito, mas o conteúdo pode não corresponder exatamente ao esperado: {path}"
            except Exception as e:
                # Se não conseguir verificar, pelo menos confirmar que o arquivo existe
                return f"Arquivo escrito com sucesso: {path} (codificação: {encoding})"
        else:
            return f"Erro: Arquivo não foi criado: {path}"

    except Exception as e:
        return f"Erro ao escrever arquivo: {str(e)}"

def read_file_unicode(path, encoding=None):
    """
    Lê o conteúdo de um arquivo com detecção automática de codificação.

    Args:
        path (str): Caminho do arquivo
        encoding (str, optional): Codificação do arquivo. Se None, detecta automaticamente.

    Returns:
        dict: Dicionário com 'content', 'encoding' e possivelmente 'error'
    """
    try:
        # Normalizar o caminho
        path = os.path.normpath(path)

        # Verificar se o arquivo existe
        if not os.path.isfile(path):
            return {
                'content': None,
                'encoding': None,
                'error': f"Arquivo não encontrado: {path}"
            }

        # Se a codificação não foi especificada, detectar automaticamente
        if encoding is None:
            # Ler os primeiros 4KB do arquivo para detectar a codificação
            with open(path, 'rb') as f:
                raw_data = f.read(4096)

            # Detectar a codificação
            detection = detect_encoding(raw_data)
            encoding = detection.get('encoding', 'utf-8')
            confidence = detection.get('confidence', 0.0)

            # Se a confiança for baixa, tentar algumas codificações comuns
            if confidence < 0.7:
                encodings_to_try = ['utf-8', 'latin-1', 'windows-1252', 'ascii']
                for enc in encodings_to_try:
                    try:
                        with codecs.open(path, 'r', encoding=enc) as f:
                            content = f.read()
                        return {
                            'content': content,
                            'encoding': enc,
                            'confidence': 1.0
                        }
                    except UnicodeDecodeError:
                        continue

        # Ler o arquivo com a codificação detectada ou especificada
        with codecs.open(path, 'r', encoding=encoding) as f:
            content = f.read()

        return {
            'content': content,
            'encoding': encoding,
            'confidence': detection.get('confidence', 1.0) if 'detection' in locals() else 1.0
        }

    except Exception as e:
        return {
            'content': None,
            'encoding': encoding,
            'error': f"Erro ao ler arquivo: {str(e)}"
        }

def encode_base64_unicode(text, encoding='utf-8'):
    """
    Codifica texto em base64, preservando caracteres Unicode.

    Args:
        text (str): Texto a ser codificado
        encoding (str): Codificação a ser usada antes da codificação base64

    Returns:
        str: Texto codificado em base64
    """
    try:
        # Codificar o texto para bytes usando a codificação especificada
        text_bytes = text.encode(encoding)

        # Codificar os bytes para base64
        base64_bytes = base64.b64encode(text_bytes)

        # Converter os bytes base64 para string
        return base64_bytes.decode('ascii')

    except Exception as e:
        return f"Erro ao codificar em base64: {str(e)}"

def decode_base64_unicode(base64_text, encoding='utf-8'):
    """
    Decodifica texto de base64 para Unicode.

    Args:
        base64_text (str): Texto codificado em base64
        encoding (str): Codificação a ser usada após a decodificação base64

    Returns:
        str: Texto decodificado
    """
    try:
        # Decodificar a string base64 para bytes
        decoded_bytes = base64.b64decode(base64_text)

        # Decodificar os bytes para string usando a codificação especificada
        return decoded_bytes.decode(encoding)

    except Exception as e:
        return f"Erro ao decodificar base64: {str(e)}"

def get_supported_encodings():
    """
    Retorna uma lista de codificações suportadas pelo sistema.

    Returns:
        list: Lista de codificações suportadas
    """
    return sorted(set(codecs.aliases.aliases.values()))

def debug_string(text):
    """
    Retorna informações de debug sobre uma string.

    Args:
        text (str): Texto para analisar

    Returns:
        str: Informações de debug sobre a string
    """
    result = []
    result.append(f"Tipo: {type(text)}")
    result.append(f"Comprimento: {len(text)} caracteres")

    if isinstance(text, str):
        result.append("Caracteres:")
        for i, char in enumerate(text[:100]):  # Limitar a 100 caracteres
            result.append(f"  {i}: '{char}' (ord={ord(char)}, hex={hex(ord(char))})")

        if len(text) > 100:
            result.append(f"  ... (mais {len(text) - 100} caracteres)")

        # Tentar codificar em diferentes codificações
        encodings = ['utf-8', 'latin-1', 'windows-1252', 'ascii']
        result.append("Representação em bytes:")
        for enc in encodings:
            try:
                bytes_repr = text.encode(enc)
                result.append(f"  {enc}: {bytes_repr}")
            except UnicodeEncodeError:
                result.append(f"  {enc}: Não pode ser codificado nesta codificação")

    elif isinstance(text, bytes):
        result.append("Bytes:")
        result.append(f"  {text}")

        # Tentar decodificar em diferentes codificações
        encodings = ['utf-8', 'latin-1', 'windows-1252', 'ascii']
        result.append("Decodificação:")
        for enc in encodings:
            try:
                decoded = text.decode(enc)
                result.append(f"  {enc}: {decoded}")
            except UnicodeDecodeError:
                result.append(f"  {enc}: Não pode ser decodificado nesta codificação")

    return "\n".join(result)

# Funções de conveniência para uso direto
def write_file(path, content, encoding='utf-8'):
    """
    Função de conveniência para escrever um arquivo com codificação Unicode,
    garantindo que as quebras de linha sejam preservadas corretamente.

    Args:
        path (str): Caminho do arquivo
        content (str): Conteúdo a ser escrito
        encoding (str): Codificação a ser usada (padrão: utf-8)

    Returns:
        str: Mensagem de sucesso ou erro
    """
    try:
        # Importar o módulo de tratamento de quebras de linha
        from line_break_handler import process_content_for_writing

        # Processar o conteúdo para garantir quebras de linha corretas
        processed_content = process_content_for_writing(content)

        return write_file_unicode(path, processed_content, encoding)
    except ImportError:
        # Fallback se o módulo não estiver disponível
        # Normalizar quebras de linha para o formato do sistema operacional
        if content is not None:
            # Primeiro, normalizar todas as quebras de linha para \n
            content = content.replace('\r\n', '\n').replace('\r', '\n')

            # Verificar se o conteúdo contém caracteres de escape literais
            if '\\n' in content and '\n' not in content:
                # Substituir caracteres de escape literais por quebras de linha reais
                content = content.replace('\\n', '\n')

            # Converter para o formato do sistema operacional
            if os.linesep != '\n':
                content = content.replace('\n', os.linesep)

        return write_file_unicode(path, content, encoding)

def read_file(path, encoding=None):
    """
    Função de conveniência para ler um arquivo com detecção automática de codificação.

    Args:
        path (str): Caminho do arquivo
        encoding (str, optional): Codificação do arquivo. Se None, detecta automaticamente.

    Returns:
        str: Conteúdo do arquivo ou mensagem de erro
    """
    result = read_file_unicode(path, encoding)
    if 'error' in result:
        return result['error']

    # Processar o conteúdo para garantir quebras de linha corretas
    try:
        from line_break_fixer import process_text
        content = process_text(result['content'], aggressive=True)
        return content
    except ImportError:
        # Fallback se o módulo não estiver disponível
        return result['content']

def encode_base64(text):
    """Função de conveniência para codificar texto em base64."""
    return encode_base64_unicode(text)

def decode_base64(base64_text):
    """Função de conveniência para decodificar texto de base64."""
    return decode_base64_unicode(base64_text)

# Teste simples se o script for executado diretamente
if __name__ == "__main__":
    # Testar escrita e leitura de arquivo com caracteres especiais
    test_text = "Olá, mundo! Este é um teste com acentuação e caracteres especiais: áéíóúçãõâêîôû."
    test_file = "teste_unicode.txt"

    print(f"Texto original: {test_text}")

    # Escrever arquivo
    result = write_file(test_file, test_text)
    print(result)

    # Ler arquivo
    content = read_file(test_file)
    print(f"Conteúdo lido: {content}")

    # Verificar se o conteúdo é igual ao original
    if content == test_text:
        print("✓ Teste bem-sucedido: o conteúdo lido é igual ao original")
    else:
        print("✗ Teste falhou: o conteúdo lido é diferente do original")
        print(debug_string(content))
