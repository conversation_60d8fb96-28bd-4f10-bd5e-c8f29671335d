"""
Interface para LLMs

Este módulo define a interface para integração com modelos de linguagem grandes (LLMs).
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Tuple

# Configurar logger
logger = logging.getLogger("augment.llm")

class LLMInterface(ABC):
    """
    Interface para modelos de linguagem grandes (LLMs).
    """
    
    @abstractmethod
    def generate(self, prompt: str, **kwargs) -> str:
        """
        Gera texto a partir de um prompt.
        
        Args:
            prompt: Prompt para geração
            **kwargs: Parâmetros adicionais para o modelo
            
        Returns:
            Texto gerado
        """
        pass
        
    @abstractmethod
    def generate_with_context(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """
        Gera texto a partir de um prompt e contexto.
        
        Args:
            prompt: Prompt para geração
            context: Lista de snippets de contexto
            **kwargs: Parâmetros adicionais para o modelo
            
        Returns:
            Texto gerado
        """
        pass
        
    @abstractmethod
    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        Gera uma resposta para uma conversa.
        
        Args:
            messages: Lista de mensagens da conversa
            **kwargs: Parâmetros adicionais para o modelo
            
        Returns:
            Resposta gerada
        """
        pass
        
    @abstractmethod
    def chat_with_context(self, messages: List[Dict[str, str]], context: List[Dict[str, Any]], **kwargs) -> str:
        """
        Gera uma resposta para uma conversa com contexto.
        
        Args:
            messages: Lista de mensagens da conversa
            context: Lista de snippets de contexto
            **kwargs: Parâmetros adicionais para o modelo
            
        Returns:
            Resposta gerada
        """
        pass
        
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """
        Obtém informações sobre o modelo.
        
        Returns:
            Informações sobre o modelo
        """
        pass
