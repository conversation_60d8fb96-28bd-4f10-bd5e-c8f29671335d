"""
Integração com LLM Local Quantizado

Este módulo implementa a integração com modelos de linguagem locais quantizados
usando a biblioteca llama-cpp-python, com suporte para processamento de prompts
de qualquer tamanho sem limitação de tokens.

Complexidade:
- Tempo: O(n) onde n é o tamanho do prompt
- Espaço: O(n) para armazenar o prompt e seus chunks
"""

import os
import json
import logging
import time
import functools
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path

from llama_cpp import Llama

from .llm_interface import LLMInterface

# Configurar logger
logger = logging.getLogger("augment.llm.local")

# Importar o processador de contexto ilimitado
try:
    from .unlimited_context import UnlimitedContextProcessor
    UNLIMITED_CONTEXT_AVAILABLE = True
    logger.info("Módulo de contexto ilimitado carregado com sucesso")
except ImportError:
    UNLIMITED_CONTEXT_AVAILABLE = False
    logger.warning("Módulo de contexto ilimitado não encontrado. Usando processamento padrão.")

class LocalLLM(LLMInterface):
    """
    Integração com LLM local quantizado usando llama-cpp-python.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Inicializa a integração com o LLM local.

        Args:
            config: Configurações do modelo e parâmetros de geração

        Complexidade:
            - Tempo: O(1) para inicialização
            - Espaço: O(1) para armazenar configurações
        """
        self.config = config or {}

        # Configurações
        self.model_path = self.config.get('model_path') or self._get_default_model_path()
        self.n_ctx = self.config.get("n_ctx", 16384)  # Aumentado para 16K
        self.n_batch = self.config.get("n_batch", 512)
        self.n_gpu_layers = self.config.get("n_gpu_layers", 0)
        self.verbose = self.config.get("verbose", False)

        # Configuração para processamento de contexto ilimitado
        self.unlimited_context = self.config.get("unlimited_context", True)

        # Configurações padrão para geração
        self.default_params = {
            "temperature": self.config.get("temperature", 0.7),
            "max_tokens": self.config.get("max_tokens", 4096),  # Aumentado para 4K
            "top_p": self.config.get("top_p", 0.95),
            "frequency_penalty": self.config.get("frequency_penalty", 0.0),
            "presence_penalty": self.config.get("presence_penalty", 0.0),
            "stop": self.config.get("stop", None)
        }

        # Estatísticas
        self.stats = {
            "total_requests": 0,
            "total_tokens": 0,
            "total_time": 0.0,
            "errors": 0,
            "unlimited_context_used": 0
        }

        # Inicializar processador de contexto ilimitado se disponível
        self.unlimited_context_processor = None
        if UNLIMITED_CONTEXT_AVAILABLE and self.unlimited_context:
            try:
                self.unlimited_context_processor = UnlimitedContextProcessor(
                    model_ctx_size=self.n_ctx,
                    overlap_ratio=self.config.get("overlap_ratio", 0.1),
                    importance_threshold=self.config.get("importance_threshold", 0.7)
                )
                logger.info("Processador de contexto ilimitado inicializado com sucesso")
            except Exception as e:
                logger.error(f"Erro ao inicializar processador de contexto ilimitado: {e}")
                self.unlimited_context_processor = None

        # Carregar modelo
        logger.info(f"Inicializando LLM local: modelo={self.model_path}")
        self._load_model()

    def _get_default_model_path(self):
        """Retorna o caminho padrão para o modelo GGUF."""
        default_path = "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf"
        logger.warning(f"Caminho do modelo não especificado. Usando padrão: {default_path}")
        return default_path

    def _load_model(self):
        """
        Carrega o modelo quantizado.
        """
        try:
            # Verificar se o valor de n_ctx é válido para o modelo
            # Alguns modelos têm limites específicos, então vamos garantir que estamos usando um valor válido
            # Valores comuns são 2048, 4096, 8192, 16384, 32768
            valid_ctx_sizes = [2048, 4096, 8192, 16384, 32768]

            # Encontrar o maior valor válido que é menor ou igual ao n_ctx solicitado
            actual_n_ctx = 4096  # Valor padrão seguro
            for size in valid_ctx_sizes:
                if size <= self.n_ctx:
                    actual_n_ctx = size

            # Se o valor solicitado for maior que o maior valor válido, usar o maior valor válido
            if self.n_ctx > max(valid_ctx_sizes):
                logger.warning(f"Valor de n_ctx solicitado ({self.n_ctx}) é maior que o máximo suportado. Usando {actual_n_ctx}.")
                self.n_ctx = actual_n_ctx

            # Carregar o modelo com o valor de n_ctx ajustado
            logger.info(f"Carregando modelo com n_ctx={self.n_ctx}")
            self.model = Llama(
                model_path=self.model_path,
                n_ctx=self.n_ctx,
                n_batch=self.n_batch,
                n_gpu_layers=self.n_gpu_layers,
                verbose=self.verbose,
                logits_all=True,
                vocab_only=False
            )
            logger.info(f"Modelo carregado com sucesso: {self.model_path}")
        except Exception as e:
            logger.error(f"Erro ao carregar modelo: {e}")

            # Se o erro for relacionado ao tamanho do contexto, tentar novamente com um valor menor
            if "n_ctx" in str(e).lower():
                try:
                    logger.warning(f"Erro ao carregar modelo com n_ctx={self.n_ctx}. Tentando com n_ctx=4096.")
                    self.n_ctx = 4096
                    self.model = Llama(
                        model_path=self.model_path,
                        n_ctx=self.n_ctx,
                        n_batch=self.n_batch,
                        n_gpu_layers=self.n_gpu_layers,
                        verbose=self.verbose,
                        logits_all=True,
                        vocab_only=False
                    )
                    logger.info(f"Modelo carregado com sucesso usando n_ctx=4096")
                except Exception as e2:
                    logger.error(f"Erro ao carregar modelo com n_ctx=4096: {e2}")
                    self.model = None
                    raise
            else:
                self.model = None
                raise

    def generate(self, prompt: str, **kwargs) -> str:
        """
        Gera texto a partir de um prompt.

        Esta função suporta prompts de qualquer tamanho, usando processamento
        de contexto ilimitado quando necessário.

        Args:
            prompt: Prompt para geração
            **kwargs: Parâmetros adicionais para o modelo

        Returns:
            Texto gerado

        Complexidade:
            - Tempo: O(n) onde n é o tamanho do prompt
            - Espaço: O(n) para armazenar o prompt e seus chunks
        """
        if self.model is None:
            logger.error("Modelo não carregado")
            return ""

        # Preparar parâmetros
        params = self.default_params.copy()
        params.update(kwargs)

        # Extrair parâmetros específicos
        max_tokens = params.pop("max_tokens", 4096)
        temperature = params.pop("temperature", 0.7)
        top_p = params.pop("top_p", 0.95)
        frequency_penalty = params.pop("frequency_penalty", 0.0)
        presence_penalty = params.pop("presence_penalty", 0.0)
        stop = params.pop("stop", None)

        # Verificar se devemos usar o processador de contexto ilimitado
        use_unlimited_context = kwargs.pop("unlimited_context", self.unlimited_context)

        # Estimar o número de tokens no prompt (aproximadamente 1 token = 4 caracteres em inglês)
        estimated_tokens = len(prompt) // 3

        # Se o processador de contexto ilimitado está disponível e o prompt é grande, usá-lo
        if (self.unlimited_context_processor is not None and
            use_unlimited_context and
            estimated_tokens > self.n_ctx - max_tokens):

            logger.info(f"Usando processador de contexto ilimitado para prompt de {estimated_tokens} tokens estimados")

            # Definir a função de geração para o processador de contexto ilimitado
            def _generate_fn(p, mt):
                return self._generate_with_model(p, max_tokens=mt, temperature=temperature,
                                               top_p=top_p, frequency_penalty=frequency_penalty,
                                               presence_penalty=presence_penalty, stop=stop)

            # Processar o prompt com o processador de contexto ilimitado
            start_time = time.time()
            try:
                generated_text = self.unlimited_context_processor.process_prompt(
                    prompt=prompt,
                    max_tokens_output=max_tokens,
                    generate_fn=_generate_fn
                )

                # Atualizar estatísticas
                self.stats["total_requests"] += 1
                self.stats["unlimited_context_used"] += 1
                self.stats["total_time"] += time.time() - start_time

                return generated_text
            except Exception as e:
                logger.error(f"Erro ao processar prompt com contexto ilimitado: {e}")
                logger.warning("Tentando com método padrão...")
                # Continuar com o método padrão

        # Método padrão (sem processador de contexto ilimitado ou se ele falhou)
        return self._generate_with_model(prompt, max_tokens=max_tokens, temperature=temperature,
                                       top_p=top_p, frequency_penalty=frequency_penalty,
                                       presence_penalty=presence_penalty, stop=stop)

    def _generate_with_model(self, prompt: str, **kwargs) -> str:
        """
        Implementação interna da geração de texto usando o modelo.

        Args:
            prompt: Prompt para geração
            **kwargs: Parâmetros para o modelo

        Returns:
            Texto gerado
        """
        # Extrair parâmetros
        max_tokens = kwargs.get("max_tokens", 4096)
        temperature = kwargs.get("temperature", 0.7)
        top_p = kwargs.get("top_p", 0.95)
        frequency_penalty = kwargs.get("frequency_penalty", 0.0)
        presence_penalty = kwargs.get("presence_penalty", 0.0)
        stop = kwargs.get("stop", None)

        # Verificar tamanho do prompt
        prompt_tokens = len(prompt.split())
        if prompt_tokens > self.n_ctx - max_tokens:
            logger.warning(f"Prompt muito grande ({prompt_tokens} tokens). Tentando ajustar...")

            # Tentar reduzir o tamanho do prompt mantendo as partes mais importantes
            # Dividir o prompt em seções
            sections = prompt.split("\n\n")

            # Identificar seções importantes (instruções do sistema, mensagem do usuário)
            important_sections = []
            other_sections = []

            for section in sections:
                # Priorizar a mensagem do usuário e instruções do sistema
                if "Mensagem do usuário:" in section or "INSTRUÇÕES" in section.upper() or "IMPORTANTE" in section.upper():
                    important_sections.append(section)
                else:
                    other_sections.append(section)

            # Reconstruir o prompt com seções importantes e outras até o limite
            new_prompt = "\n\n".join(important_sections)
            remaining_tokens = self.n_ctx - max_tokens - len(new_prompt.split())

            # Adicionar outras seções até o limite
            for section in other_sections:
                section_tokens = len(section.split())
                if section_tokens < remaining_tokens:
                    new_prompt += f"\n\n{section}"
                    remaining_tokens -= section_tokens
                else:
                    break

            logger.info(f"Prompt reduzido de {prompt_tokens} para {len(new_prompt.split())} tokens")
            prompt = new_prompt

        # Gerar texto
        start_time = time.time()
        try:
            output = self.model(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                repeat_penalty=1.0 + presence_penalty,
                frequency_penalty=frequency_penalty,
                stop=stop
            )

            # Extrair texto gerado
            generated_text = output["choices"][0]["text"]

            # Atualizar estatísticas
            self.stats["total_requests"] += 1
            self.stats["total_tokens"] += len(output["choices"][0]["text"].split())
            self.stats["total_time"] += time.time() - start_time

            return generated_text
        except Exception as e:
            logger.error(f"Erro ao gerar texto: {e}")

            # Tentar novamente com um prompt menor se o erro for relacionado ao tamanho do contexto
            if "context window" in str(e).lower() and len(prompt) > 1000:
                logger.warning("Erro de contexto. Tentando com um prompt menor...")

                # Reduzir drasticamente o prompt, mantendo apenas as partes essenciais
                lines = prompt.split("\n")
                essential_lines = []

                # Manter apenas as últimas linhas (geralmente contêm a mensagem do usuário)
                user_message_start = -1
                for i, line in enumerate(lines):
                    if "Mensagem do usuário:" in line:
                        user_message_start = i
                        break

                if user_message_start >= 0:
                    # Manter as primeiras 10 linhas (instruções do sistema) e a mensagem do usuário
                    essential_lines = lines[:10] + lines[user_message_start:]
                else:
                    # Se não encontrar a mensagem do usuário, manter as primeiras 10 e últimas 20 linhas
                    essential_lines = lines[:10] + lines[-20:]

                reduced_prompt = "\n".join(essential_lines)
                logger.info(f"Tentando novamente com prompt reduzido de {len(prompt)} para {len(reduced_prompt)} caracteres")

                try:
                    output = self.model(
                        reduced_prompt,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        top_p=top_p,
                        repeat_penalty=1.0 + presence_penalty,
                        frequency_penalty=frequency_penalty,
                        stop=stop
                    )

                    # Extrair texto gerado
                    generated_text = output["choices"][0]["text"]

                    # Atualizar estatísticas
                    self.stats["total_requests"] += 1
                    self.stats["total_tokens"] += len(output["choices"][0]["text"].split())
                    self.stats["total_time"] += time.time() - start_time

                    return generated_text
                except Exception as e2:
                    logger.error(f"Erro na segunda tentativa: {e2}")
                    self.stats["errors"] += 1
                    return "Erro ao processar o prompt devido ao tamanho. Por favor, tente uma solicitação mais curta."

            self.stats["errors"] += 1
            return ""

    def generate_with_context(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """
        Gera texto a partir de um prompt e contexto.

        Args:
            prompt: Prompt para geração
            context: Lista de snippets de contexto
            **kwargs: Parâmetros adicionais para o modelo

        Returns:
            Texto gerado
        """
        # Formatar contexto
        formatted_context = self._format_context(context)

        # Combinar prompt e contexto
        combined_prompt = f"{formatted_context}\n\n{prompt}"

        # Gerar texto
        return self.generate(combined_prompt, **kwargs)

    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        Gera uma resposta para uma conversa.

        Esta função suporta conversas de qualquer tamanho, usando processamento
        de contexto ilimitado quando necessário.

        Args:
            messages: Lista de mensagens da conversa
            **kwargs: Parâmetros adicionais para o modelo

        Returns:
            Resposta gerada

        Complexidade:
            - Tempo: O(n) onde n é o tamanho total das mensagens
            - Espaço: O(n) para armazenar as mensagens e seus chunks
        """
        if self.model is None:
            logger.error("Modelo não carregado")
            return ""

        # Preparar parâmetros
        params = self.default_params.copy()
        params.update(kwargs)

        # Extrair parâmetros específicos
        max_tokens = params.pop("max_tokens", 4096)
        temperature = params.pop("temperature", 0.7)
        top_p = params.pop("top_p", 0.95)
        frequency_penalty = params.pop("frequency_penalty", 0.0)
        presence_penalty = params.pop("presence_penalty", 0.0)
        stop = params.pop("stop", None)

        # Verificar se devemos usar o processador de contexto ilimitado
        use_unlimited_context = kwargs.pop("unlimited_context", self.unlimited_context)

        # Formatar mensagens para o formato do modelo
        formatted_messages = self._format_messages(messages)

        # Estimar o número total de tokens nas mensagens
        total_tokens = sum(len(msg.get("content", "").split()) for msg in formatted_messages)

        # Se o processador de contexto ilimitado está disponível e as mensagens são grandes, usá-lo
        if (self.unlimited_context_processor is not None and
            use_unlimited_context and
            total_tokens > self.n_ctx - max_tokens):

            logger.info(f"Usando processador de contexto ilimitado para chat com {total_tokens} tokens estimados")

            # Definir a função de chat para o processador de contexto ilimitado
            def _chat_fn(msgs, mt):
                return self._chat_with_model(msgs, max_tokens=mt, temperature=temperature,
                                           top_p=top_p, frequency_penalty=frequency_penalty,
                                           presence_penalty=presence_penalty, stop=stop)

            # Processar as mensagens com o processador de contexto ilimitado
            start_time = time.time()
            try:
                generated_text = self.unlimited_context_processor.process_chat(
                    messages=formatted_messages,
                    max_tokens_output=max_tokens,
                    chat_fn=_chat_fn
                )

                # Atualizar estatísticas
                self.stats["total_requests"] += 1
                self.stats["unlimited_context_used"] += 1
                self.stats["total_time"] += time.time() - start_time

                return generated_text
            except Exception as e:
                logger.error(f"Erro ao processar chat com contexto ilimitado: {e}")
                logger.warning("Tentando com método padrão...")
                # Continuar com o método padrão

        # Método padrão (sem processador de contexto ilimitado ou se ele falhou)
        return self._chat_with_model(formatted_messages, max_tokens=max_tokens, temperature=temperature,
                                   top_p=top_p, frequency_penalty=frequency_penalty,
                                   presence_penalty=presence_penalty, stop=stop)

    def _chat_with_model(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        Implementação interna da geração de resposta de chat usando o modelo.

        Args:
            messages: Lista de mensagens formatadas
            **kwargs: Parâmetros para o modelo

        Returns:
            Texto gerado
        """
        # Extrair parâmetros
        max_tokens = kwargs.get("max_tokens", 4096)
        temperature = kwargs.get("temperature", 0.7)
        top_p = kwargs.get("top_p", 0.95)
        frequency_penalty = kwargs.get("frequency_penalty", 0.0)
        presence_penalty = kwargs.get("presence_penalty", 0.0)
        stop = kwargs.get("stop", None)

        # Verificar tamanho total das mensagens
        total_tokens = sum(len(msg.get("content", "").split()) for msg in messages)
        if total_tokens > self.n_ctx - max_tokens:
            logger.warning(f"Mensagens muito grandes ({total_tokens} tokens). Tentando ajustar...")

            # Preservar a mensagem do sistema e a última mensagem do usuário
            system_message = None
            user_messages = []
            assistant_messages = []

            for msg in messages:
                if msg.get("role") == "system":
                    system_message = msg
                elif msg.get("role") == "user":
                    user_messages.append(msg)
                elif msg.get("role") == "assistant":
                    assistant_messages.append(msg)

            # Reconstruir as mensagens com prioridade
            new_messages = []

            # Adicionar mensagem do sistema se existir
            if system_message:
                new_messages.append(system_message)

            # Adicionar a última mensagem do usuário
            if user_messages:
                new_messages.append(user_messages[-1])

            # Adicionar mensagens alternadas de usuário e assistente, começando pelas mais recentes
            remaining_tokens = self.n_ctx - max_tokens - sum(len(msg.get("content", "").split()) for msg in new_messages)

            # Criar pares de mensagens (assistente seguido pelo usuário correspondente)
            message_pairs = []
            for i in range(min(len(assistant_messages), len(user_messages) - 1)):
                # Associar cada resposta do assistente com a pergunta anterior do usuário
                message_pairs.append((assistant_messages[-(i+1)], user_messages[-(i+2)]))

            # Adicionar pares de mensagens até o limite
            for assistant_msg, user_msg in message_pairs:
                pair_tokens = len(assistant_msg.get("content", "").split()) + len(user_msg.get("content", "").split())
                if pair_tokens < remaining_tokens:
                    # Inserir no início para manter a ordem cronológica
                    new_messages.insert(1, user_msg)
                    new_messages.insert(2, assistant_msg)
                    remaining_tokens -= pair_tokens
                else:
                    break

            logger.info(f"Mensagens reduzidas de {total_tokens} para {sum(len(msg.get('content', '').split()) for msg in new_messages)} tokens")
            messages = new_messages

        # Gerar resposta
        start_time = time.time()
        try:
            output = self.model.create_chat_completion(
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                repeat_penalty=1.0 + presence_penalty,
                frequency_penalty=frequency_penalty,
                stop=stop
            )

            # Extrair texto gerado
            generated_text = output["choices"][0]["message"]["content"]

            # Atualizar estatísticas
            self.stats["total_requests"] += 1
            self.stats["total_tokens"] += len(generated_text.split())
            self.stats["total_time"] += time.time() - start_time

            return generated_text
        except Exception as e:
            logger.error(f"Erro ao gerar resposta de chat: {e}")

            # Tentar novamente com menos mensagens se o erro for relacionado ao tamanho do contexto
            if "context window" in str(e).lower() and len(messages) > 2:
                logger.warning("Erro de contexto. Tentando com menos mensagens...")

                # Manter apenas a mensagem do sistema (se existir) e a última mensagem do usuário
                reduced_messages = []

                # Encontrar a mensagem do sistema
                system_message = next((msg for msg in messages if msg.get("role") == "system"), None)
                if system_message:
                    reduced_messages.append(system_message)

                # Encontrar a última mensagem do usuário
                user_message = next((msg for msg in reversed(messages) if msg.get("role") == "user"), None)
                if user_message:
                    reduced_messages.append(user_message)

                # Se não encontrou mensagens, usar uma mensagem padrão
                if not reduced_messages:
                    reduced_messages = [{"role": "user", "content": "Por favor, continue."}]

                logger.info(f"Tentando novamente com {len(reduced_messages)} mensagens")

                try:
                    output = self.model.create_chat_completion(
                        messages=reduced_messages,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        top_p=top_p,
                        repeat_penalty=1.0 + presence_penalty,
                        frequency_penalty=frequency_penalty,
                        stop=stop
                    )

                    # Extrair texto gerado
                    generated_text = output["choices"][0]["message"]["content"]

                    # Atualizar estatísticas
                    self.stats["total_requests"] += 1
                    self.stats["total_tokens"] += len(generated_text.split())
                    self.stats["total_time"] += time.time() - start_time

                    return generated_text
                except Exception as e2:
                    logger.error(f"Erro na segunda tentativa: {e2}")
                    self.stats["errors"] += 1
                    return "Erro ao processar as mensagens devido ao tamanho. Por favor, tente uma conversa mais curta."

            self.stats["errors"] += 1
            return ""

    def chat_with_context(self, messages: List[Dict[str, str]], context: List[Dict[str, Any]], **kwargs) -> str:
        """
        Gera uma resposta para uma conversa com contexto.

        Args:
            messages: Lista de mensagens da conversa
            context: Lista de snippets de contexto
            **kwargs: Parâmetros adicionais para o modelo

        Returns:
            Resposta gerada
        """
        # Formatar contexto
        formatted_context = self._format_context(context)

        # Adicionar contexto como mensagem do sistema
        system_message = {
            "role": "system",
            "content": f"Você tem acesso ao seguinte contexto relevante:\n\n{formatted_context}"
        }

        # Verificar se já existe uma mensagem do sistema
        if messages and messages[0].get("role") == "system":
            # Combinar com a mensagem do sistema existente
            messages[0]["content"] = f"{messages[0]['content']}\n\n{system_message['content']}"
        else:
            # Adicionar nova mensagem do sistema
            messages = [system_message] + messages

        # Gerar resposta
        return self.chat(messages, **kwargs)

    def _format_messages(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        Formata mensagens para o formato esperado pelo modelo.

        Args:
            messages: Lista de mensagens

        Returns:
            Lista de mensagens formatadas
        """
        # Verificar se as mensagens já estão no formato correto
        if all(set(msg.keys()).issuperset({"role", "content"}) for msg in messages):
            return messages

        # Caso contrário, converter para o formato correto
        formatted_messages = []
        for msg in messages:
            if "role" in msg and "content" in msg:
                formatted_messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            elif "text" in msg:
                # Tentar inferir o papel com base no conteúdo
                if "user:" in msg["text"].lower():
                    role = "user"
                elif "assistant:" in msg["text"].lower():
                    role = "assistant"
                else:
                    role = "user"  # Padrão

                formatted_messages.append({
                    "role": role,
                    "content": msg["text"]
                })

        return formatted_messages

    def _format_context(self, context: List[Dict[str, Any]]) -> str:
        """
        Formata o contexto para inclusão no prompt.

        Args:
            context: Lista de snippets de contexto

        Returns:
            Contexto formatado
        """
        formatted_snippets = []

        for i, snippet in enumerate(context):
            content = snippet.get("content", "")
            file_path = snippet.get("file_path", "unknown")
            start_line = snippet.get("start_line", 1)
            end_line = snippet.get("end_line", start_line + content.count("\n"))
            language = snippet.get("language", "")

            formatted_snippet = f"[SNIPPET {i+1}] {file_path}:{start_line}-{end_line} ({language})\n```{language}\n{content}\n```"
            formatted_snippets.append(formatted_snippet)

        return "\n\n".join(formatted_snippets)

    def get_model_info(self) -> Dict[str, Any]:
        """
        Obtém informações sobre o modelo.

        Returns:
            Informações sobre o modelo
        """
        return {
            "name": Path(self.model_path).stem,
            "path": self.model_path,
            "n_ctx": self.n_ctx,
            "n_gpu_layers": self.n_gpu_layers,
            "stats": self.stats
        }

    # Adicionei suporte específico para arquitetura Gemma3
    # Na classe LocalLLM, adicione:
    class LocalLLM(LLMInterface):
        def __init__(self, config):
            super().__init__(config)
            self.model_path = config['local'].get('model_path') or self._get_default_model_path()
            self._verify_model_file()
            self._initialize_model()
    
        def _get_default_model_path(self):
            return "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf"

        def _initialize_model(self):
            self.llm = Llama(
                model_path=self.model_path,
                n_ctx=4096,
                n_gpu_layers=-1,
                use_mlock=True,
                logits_all=True,
                vocab_only=False
            )
            self.llm = Llama(
                model_path=model_path,
                n_ctx=4096,
                n_gpu_layers=-1,
                logits_all=True,
                vocab_only=False,
                use_mlock=True,
                n_threads=6
            )
