"""
Ferramentas de Processo

Este módulo implementa ferramentas para execução e gerenciamento de processos.
"""

import os
import sys
import logging
import subprocess
import threading
import time
import signal
import psutil
from typing import Dict, List, Any, Optional, Union, Tuple

from tools.tool_interface import Tool, ToolRegistry

# Configurar logger
logger = logging.getLogger("augment.tools.process")

class ProcessManager:
    """
    Gerenciador de processos.
    """
    
    def __init__(self):
        """
        Inicializa o gerenciador de processos.
        """
        self.processes: Dict[int, Dict[str, Any]] = {}
        self.lock = threading.RLock()
        self.next_id = 1
        
    def start_process(self, command: str, cwd: Optional[str] = None, env: Optional[Dict[str, str]] = None,
                     shell: bool = True, capture_output: bool = True) -> Dict[str, Any]:
        """
        Inicia um processo.
        
        Args:
            command: Comando a ser executado
            cwd: Diretório de trabalho
            env: Variáveis de ambiente
            shell: Se deve usar shell
            capture_output: Se deve capturar saída
            
        Returns:
            Informações do processo
        """
        with self.lock:
            process_id = self.next_id
            self.next_id += 1
            
        try:
            # Preparar ambiente
            process_env = os.environ.copy()
            if env:
                process_env.update(env)
                
            # Iniciar processo
            if capture_output:
                process = subprocess.Popen(
                    command,
                    cwd=cwd,
                    env=process_env,
                    shell=shell,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )
            else:
                process = subprocess.Popen(
                    command,
                    cwd=cwd,
                    env=process_env,
                    shell=shell
                )
                
            # Registrar processo
            process_info = {
                "id": process_id,
                "command": command,
                "process": process,
                "pid": process.pid,
                "start_time": time.time(),
                "status": "running",
                "stdout": "",
                "stderr": "",
                "exit_code": None
            }
            
            with self.lock:
                self.processes[process_id] = process_info
                
            # Iniciar threads para capturar saída
            if capture_output:
                self._start_output_threads(process_id)
                
            return {
                "id": process_id,
                "pid": process.pid,
                "command": command,
                "status": "running"
            }
        except Exception as e:
            logger.error(f"Erro ao iniciar processo: {e}")
            
            return {
                "id": process_id,
                "error": str(e),
                "status": "failed"
            }
            
    def _start_output_threads(self, process_id: int) -> None:
        """
        Inicia threads para capturar saída do processo.
        
        Args:
            process_id: ID do processo
        """
        process_info = self.processes.get(process_id)
        if not process_info:
            return
            
        process = process_info["process"]
        
        def read_stdout():
            for line in iter(process.stdout.readline, ""):
                with self.lock:
                    if process_id in self.processes:
                        self.processes[process_id]["stdout"] += line
                        
            process.stdout.close()
            
        def read_stderr():
            for line in iter(process.stderr.readline, ""):
                with self.lock:
                    if process_id in self.processes:
                        self.processes[process_id]["stderr"] += line
                        
            process.stderr.close()
            
        # Iniciar threads
        stdout_thread = threading.Thread(target=read_stdout)
        stdout_thread.daemon = True
        stdout_thread.start()
        
        stderr_thread = threading.Thread(target=read_stderr)
        stderr_thread.daemon = True
        stderr_thread.start()
        
        # Thread para monitorar término do processo
        def monitor_process():
            exit_code = process.wait()
            
            with self.lock:
                if process_id in self.processes:
                    self.processes[process_id]["status"] = "finished"
                    self.processes[process_id]["exit_code"] = exit_code
                    self.processes[process_id]["end_time"] = time.time()
                    
        monitor_thread = threading.Thread(target=monitor_process)
        monitor_thread.daemon = True
        monitor_thread.start()
        
    def get_process(self, process_id: int) -> Optional[Dict[str, Any]]:
        """
        Obtém informações de um processo.
        
        Args:
            process_id: ID do processo
            
        Returns:
            Informações do processo ou None se não encontrado
        """
        with self.lock:
            process_info = self.processes.get(process_id)
            
            if not process_info:
                return None
                
            # Verificar se o processo ainda está em execução
            if process_info["status"] == "running":
                try:
                    # Verificar se o processo ainda existe
                    if not psutil.pid_exists(process_info["pid"]):
                        process_info["status"] = "finished"
                        process_info["exit_code"] = -1
                        process_info["end_time"] = time.time()
                except Exception:
                    pass
                    
            # Criar cópia das informações
            result = {
                "id": process_info["id"],
                "command": process_info["command"],
                "pid": process_info["pid"],
                "status": process_info["status"],
                "start_time": process_info["start_time"]
            }
            
            if "stdout" in process_info:
                result["stdout"] = process_info["stdout"]
                
            if "stderr" in process_info:
                result["stderr"] = process_info["stderr"]
                
            if "exit_code" in process_info and process_info["exit_code"] is not None:
                result["exit_code"] = process_info["exit_code"]
                
            if "end_time" in process_info:
                result["end_time"] = process_info["end_time"]
                result["duration"] = process_info["end_time"] - process_info["start_time"]
                
            return result
            
    def kill_process(self, process_id: int) -> bool:
        """
        Mata um processo.
        
        Args:
            process_id: ID do processo
            
        Returns:
            True se o processo foi morto, False caso contrário
        """
        with self.lock:
            process_info = self.processes.get(process_id)
            
            if not process_info:
                return False
                
            if process_info["status"] != "running":
                return False
                
            try:
                # Tentar matar o processo
                process = process_info["process"]
                pid = process_info["pid"]
                
                # Tentar matar o processo e seus filhos
                parent = psutil.Process(pid)
                
                for child in parent.children(recursive=True):
                    try:
                        child.kill()
                    except:
                        pass
                        
                parent.kill()
                
                # Atualizar informações
                process_info["status"] = "killed"
                process_info["end_time"] = time.time()
                
                return True
            except Exception as e:
                logger.error(f"Erro ao matar processo {process_id}: {e}")
                return False
                
    def list_processes(self) -> List[Dict[str, Any]]:
        """
        Lista todos os processos.
        
        Returns:
            Lista de informações de processos
        """
        with self.lock:
            return [self.get_process(process_id) for process_id in self.processes]
            
    def cleanup(self) -> None:
        """
        Limpa processos terminados.
        """
        with self.lock:
            to_remove = []
            
            for process_id, process_info in self.processes.items():
                if process_info["status"] != "running":
                    to_remove.append(process_id)
                    
            for process_id in to_remove:
                del self.processes[process_id]

class ProcessTools:
    """
    Ferramentas para execução e gerenciamento de processos.
    """
    
    def __init__(self, registry: ToolRegistry):
        """
        Inicializa as ferramentas de processo.
        
        Args:
            registry: Registro de ferramentas
        """
        self.registry = registry
        self.process_manager = ProcessManager()
        
        # Registrar ferramentas
        self._register_tools()
        
    def _register_tools(self):
        """
        Registra as ferramentas de processo.
        """
        # Ferramenta para executar comando
        self.registry.register(Tool(
            name="execute_command",
            description="Executa um comando e espera pela conclusão",
            function=self.execute_command,
            parameters={
                "command": {
                    "type": "string",
                    "description": "Comando a ser executado",
                    "required": True
                },
                "cwd": {
                    "type": "string",
                    "description": "Diretório de trabalho",
                    "required": False
                },
                "timeout": {
                    "type": "integer",
                    "description": "Tempo limite em segundos",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para iniciar processo
        self.registry.register(Tool(
            name="start_process",
            description="Inicia um processo em segundo plano",
            function=self.start_process,
            parameters={
                "command": {
                    "type": "string",
                    "description": "Comando a ser executado",
                    "required": True
                },
                "cwd": {
                    "type": "string",
                    "description": "Diretório de trabalho",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para obter informações de processo
        self.registry.register(Tool(
            name="get_process",
            description="Obtém informações de um processo",
            function=self.get_process,
            parameters={
                "process_id": {
                    "type": "integer",
                    "description": "ID do processo",
                    "required": True
                }
            }
        ))
        
        # Ferramenta para matar processo
        self.registry.register(Tool(
            name="kill_process",
            description="Mata um processo",
            function=self.kill_process,
            parameters={
                "process_id": {
                    "type": "integer",
                    "description": "ID do processo",
                    "required": True
                }
            }
        ))
        
        # Ferramenta para listar processos
        self.registry.register(Tool(
            name="list_processes",
            description="Lista todos os processos",
            function=self.list_processes
        ))
        
    def execute_command(self, command: str, cwd: Optional[str] = None, timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Executa um comando e espera pela conclusão.
        
        Args:
            command: Comando a ser executado
            cwd: Diretório de trabalho
            timeout: Tempo limite em segundos
            
        Returns:
            Resultado da execução
        """
        logger.info(f"Executando comando: {command}")
        
        try:
            # Executar comando
            process = subprocess.run(
                command,
                cwd=cwd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            return {
                "stdout": process.stdout,
                "stderr": process.stderr,
                "exit_code": process.returncode,
                "success": process.returncode == 0
            }
        except subprocess.TimeoutExpired as e:
            logger.warning(f"Comando atingiu o tempo limite: {command}")
            
            return {
                "error": f"Tempo limite atingido ({timeout}s)",
                "stdout": e.stdout.decode('utf-8') if e.stdout else "",
                "stderr": e.stderr.decode('utf-8') if e.stderr else "",
                "exit_code": None,
                "success": False
            }
        except Exception as e:
            logger.error(f"Erro ao executar comando: {e}")
            
            return {
                "error": str(e),
                "exit_code": None,
                "success": False
            }
            
    def start_process(self, command: str, cwd: Optional[str] = None) -> Dict[str, Any]:
        """
        Inicia um processo em segundo plano.
        
        Args:
            command: Comando a ser executado
            cwd: Diretório de trabalho
            
        Returns:
            Informações do processo
        """
        logger.info(f"Iniciando processo: {command}")
        
        return self.process_manager.start_process(command, cwd)
        
    def get_process(self, process_id: int) -> Dict[str, Any]:
        """
        Obtém informações de um processo.
        
        Args:
            process_id: ID do processo
            
        Returns:
            Informações do processo
        """
        logger.info(f"Obtendo informações do processo: {process_id}")
        
        process_info = self.process_manager.get_process(process_id)
        
        if not process_info:
            return {"error": f"Processo não encontrado: {process_id}"}
            
        return process_info
        
    def kill_process(self, process_id: int) -> Dict[str, Any]:
        """
        Mata um processo.
        
        Args:
            process_id: ID do processo
            
        Returns:
            Resultado da operação
        """
        logger.info(f"Matando processo: {process_id}")
        
        success = self.process_manager.kill_process(process_id)
        
        if success:
            return {"success": True, "message": f"Processo {process_id} morto com sucesso"}
        else:
            return {"success": False, "error": f"Não foi possível matar o processo {process_id}"}
            
    def list_processes(self) -> List[Dict[str, Any]]:
        """
        Lista todos os processos.
        
        Returns:
            Lista de informações de processos
        """
        logger.info("Listando processos")
        
        # Limpar processos terminados
        self.process_manager.cleanup()
        
        return self.process_manager.list_processes()
