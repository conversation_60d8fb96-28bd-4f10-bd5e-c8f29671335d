"""
Sistema de Aprendizado Contínuo

Este módulo implementa o sistema de aprendizado contínuo do Augment Agent.
"""

import os
import json
import logging
import time
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
import numpy as np

# Configurar logger
logger = logging.getLogger("augment.learning")

class LearningSystem:
    """
    Sistema de aprendizado contínuo que permite ao agente melhorar com o tempo.
    """
    
    def __init__(self, cache_dir: str = "./learning_cache", config: Optional[Dict[str, Any]] = None):
        """
        Inicializa o sistema de aprendizado.
        
        Args:
            cache_dir: Diretório para armazenar dados de aprendizado
            config: Configuração do sistema
        """
        self.config = config or {}
        self.cache_dir = Path(cache_dir)
        
        # Criar diretório de cache se não existir
        self.cache_dir.mkdir(exist_ok=True, parents=True)
        
        # Arquivo de memória
        self.memory_file = self.cache_dir / "memory.json"
        
        # Arquivo de feedback
        self.feedback_file = self.cache_dir / "feedback.json"
        
        # Arquivo de exemplos
        self.examples_file = self.cache_dir / "examples.json"
        
        # Carregar dados
        self.memory = self._load_json(self.memory_file, {})
        self.feedback = self._load_json(self.feedback_file, {})
        self.examples = self._load_json(self.examples_file, [])
        
        # Estatísticas
        self.stats = {
            'memory_accesses': 0,
            'memory_hits': 0,
            'feedback_count': len(self.feedback),
            'examples_count': len(self.examples)
        }
        
    def _load_json(self, file_path: Path, default: Any) -> Any:
        """
        Carrega dados JSON de um arquivo.
        
        Args:
            file_path: Caminho do arquivo
            default: Valor padrão se o arquivo não existir
            
        Returns:
            Dados carregados
        """
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Erro ao carregar {file_path}: {e}")
                return default
        return default
        
    def _save_json(self, file_path: Path, data: Any) -> bool:
        """
        Salva dados JSON em um arquivo.
        
        Args:
            file_path: Caminho do arquivo
            data: Dados a serem salvos
            
        Returns:
            True se salvou com sucesso, False caso contrário
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar {file_path}: {e}")
            return False
            
    def remember(self, key: str, value: Any) -> bool:
        """
        Armazena um item na memória.
        
        Args:
            key: Chave do item
            value: Valor do item
            
        Returns:
            True se armazenou com sucesso, False caso contrário
        """
        self.memory[key] = {
            'value': value,
            'timestamp': time.time()
        }
        
        return self._save_json(self.memory_file, self.memory)
        
    def recall(self, key: str) -> Optional[Any]:
        """
        Recupera um item da memória.
        
        Args:
            key: Chave do item
            
        Returns:
            Valor do item ou None se não encontrado
        """
        self.stats['memory_accesses'] += 1
        
        if key in self.memory:
            self.stats['memory_hits'] += 1
            return self.memory[key]['value']
            
        return None
        
    def add_feedback(self, query: str, response: str, rating: int, comment: Optional[str] = None) -> bool:
        """
        Adiciona feedback do usuário.
        
        Args:
            query: Consulta do usuário
            response: Resposta do agente
            rating: Avaliação (1-5)
            comment: Comentário opcional
            
        Returns:
            True se adicionou com sucesso, False caso contrário
        """
        # Gerar ID único para o feedback
        feedback_id = hashlib.md5(f"{query}:{response}:{time.time()}".encode('utf-8')).hexdigest()
        
        # Adicionar feedback
        self.feedback[feedback_id] = {
            'query': query,
            'response': response,
            'rating': rating,
            'comment': comment,
            'timestamp': time.time()
        }
        
        self.stats['feedback_count'] = len(self.feedback)
        
        # Se o feedback for muito bom (4-5), adicionar como exemplo
        if rating >= 4:
            self._add_example(query, response)
            
        return self._save_json(self.feedback_file, self.feedback)
        
    def _add_example(self, query: str, response: str) -> bool:
        """
        Adiciona um exemplo de boa resposta.
        
        Args:
            query: Consulta do usuário
            response: Resposta do agente
            
        Returns:
            True se adicionou com sucesso, False caso contrário
        """
        # Verificar se já existe um exemplo similar
        for example in self.examples:
            if self._is_similar(query, example['query']):
                # Atualizar exemplo existente se a nova resposta for melhor
                if len(response) > len(example['response']):
                    example['response'] = response
                    example['timestamp'] = time.time()
                    self._save_json(self.examples_file, self.examples)
                return True
                
        # Adicionar novo exemplo
        self.examples.append({
            'query': query,
            'response': response,
            'timestamp': time.time()
        })
        
        self.stats['examples_count'] = len(self.examples)
        
        return self._save_json(self.examples_file, self.examples)
        
    def _is_similar(self, query1: str, query2: str) -> bool:
        """
        Verifica se duas consultas são similares.
        
        Args:
            query1: Primeira consulta
            query2: Segunda consulta
            
        Returns:
            True se as consultas forem similares, False caso contrário
        """
        # Implementação simples baseada em palavras comuns
        words1 = set(query1.lower().split())
        words2 = set(query2.lower().split())
        
        # Calcular interseção
        common_words = words1.intersection(words2)
        
        # Calcular similaridade de Jaccard
        similarity = len(common_words) / (len(words1) + len(words2) - len(common_words))
        
        # Considerar similar se a similaridade for maior que 0.5
        return similarity > 0.5
        
    def get_relevant_examples(self, query: str, max_examples: int = 3) -> List[Dict[str, Any]]:
        """
        Obtém exemplos relevantes para uma consulta.
        
        Args:
            query: Consulta do usuário
            max_examples: Número máximo de exemplos
            
        Returns:
            Lista de exemplos relevantes
        """
        if not self.examples:
            return []
            
        # Calcular similaridade com cada exemplo
        similarities = []
        for i, example in enumerate(self.examples):
            similarity = self._calculate_similarity(query, example['query'])
            similarities.append((i, similarity))
            
        # Ordenar por similaridade
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # Obter os exemplos mais relevantes
        relevant_examples = []
        for i, similarity in similarities[:max_examples]:
            if similarity > 0.3:  # Limiar de similaridade
                relevant_examples.append(self.examples[i])
                
        return relevant_examples
        
    def _calculate_similarity(self, query1: str, query2: str) -> float:
        """
        Calcula a similaridade entre duas consultas.
        
        Args:
            query1: Primeira consulta
            query2: Segunda consulta
            
        Returns:
            Similaridade entre as consultas (0-1)
        """
        # Implementação simples baseada em palavras comuns
        words1 = set(query1.lower().split())
        words2 = set(query2.lower().split())
        
        # Calcular interseção
        common_words = words1.intersection(words2)
        
        # Calcular similaridade de Jaccard
        if not words1 or not words2:
            return 0.0
            
        similarity = len(common_words) / (len(words1) + len(words2) - len(common_words))
        
        return similarity
        
    def get_system_prompt_enhancement(self) -> str:
        """
        Gera um aprimoramento para o prompt do sistema com base nos exemplos.
        
        Returns:
            Texto para adicionar ao prompt do sistema
        """
        if not self.examples:
            return ""
            
        # Selecionar os exemplos mais recentes
        recent_examples = sorted(self.examples, key=lambda x: x['timestamp'], reverse=True)[:5]
        
        # Gerar texto
        enhancement = "Aqui estão alguns exemplos de boas respostas:\n\n"
        
        for example in recent_examples:
            enhancement += f"Pergunta: {example['query']}\n"
            enhancement += f"Resposta: {example['response']}\n\n"
            
        return enhancement
        
    def get_stats(self) -> Dict[str, Any]:
        """
        Obtém estatísticas do sistema de aprendizado.
        
        Returns:
            Estatísticas
        """
        return {
            'memory_size': len(self.memory),
            'memory_accesses': self.stats['memory_accesses'],
            'memory_hits': self.stats['memory_hits'],
            'feedback_count': self.stats['feedback_count'],
            'examples_count': self.stats['examples_count'],
            'hit_rate': self.stats['memory_hits'] / max(1, self.stats['memory_accesses'])
        }
