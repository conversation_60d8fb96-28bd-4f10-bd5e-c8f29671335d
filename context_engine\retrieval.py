"""
Recuperação de Código

Este módulo implementa a recuperação de código para o motor de contexto,
permitindo a busca eficiente de snippets de código relevantes.
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
import time

# Configurar logger
logger = logging.getLogger("augment.retrieval")

class CodeRetriever:
    """
    Recuperador de código que busca snippets relevantes em um índice.
    """
    
    def __init__(self, index, config: Optional[Dict[str, Any]] = None):
        """
        Inicializa o recuperador de código.
        
        Args:
            index: Índice de código
            config: Configuração do recuperador
        """
        self.index = index
        self.config = config or {}
        
        # Configurações
        self.max_results = self.config.get('max_results', 5)
        self.min_similarity = self.config.get('min_similarity', 0.5)
        self.use_reranking = self.config.get('use_reranking', True)
        
    def retrieve(self, query_embedding: np.ndarray, max_results: Optional[int] = None, 
                filter_criteria: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Recupera snippets de código relevantes para um embedding de consulta.
        
        Args:
            query_embedding: Embedding da consulta
            max_results: Número máximo de resultados (opcional)
            filter_criteria: Critérios de filtro para os resultados (opcional)
            
        Returns:
            Lista de snippets de código relevantes com metadados
        """
        # Usar max_results padrão se não fornecido
        if max_results is None:
            max_results = self.max_results
            
        # Buscar no índice
        start_time = time.time()
        search_results = self.index.search(
            query_embedding, 
            top_k=max(max_results * 2, 10),  # Buscar mais resultados para reranking
            filter_criteria=filter_criteria
        )
        search_time = time.time() - start_time
        
        logger.debug(f"Busca concluída em {search_time:.4f} segundos. {len(search_results)} resultados encontrados.")
        
        # Filtrar por similaridade mínima
        filtered_results = [
            (idx, similarity, metadata) 
            for idx, similarity, metadata in search_results 
            if similarity >= self.min_similarity
        ]
        
        # Reranking se necessário
        if self.use_reranking and len(filtered_results) > max_results:
            start_time = time.time()
            reranked_results = self._rerank_results(filtered_results, query_embedding)
            rerank_time = time.time() - start_time
            
            logger.debug(f"Reranking concluído em {rerank_time:.4f} segundos.")
            
            # Limitar ao número máximo de resultados
            results = reranked_results[:max_results]
        else:
            # Limitar ao número máximo de resultados
            results = filtered_results[:max_results]
            
        # Converter para o formato de resultado final
        final_results = []
        for idx, similarity, metadata in results:
            # Obter o chunk completo
            chunk = self.index.get(idx)
            
            if chunk:
                final_results.append({
                    'content': chunk.content,
                    'file_path': chunk.file_path,
                    'start_line': chunk.start_line,
                    'end_line': chunk.end_line,
                    'language': chunk.language,
                    'symbols': chunk.symbols,
                    'similarity': float(similarity),
                    'metadata': metadata
                })
                
        return final_results
        
    def _rerank_results(self, results: List[Tuple[int, float, Dict[str, Any]]], 
                       query_embedding: np.ndarray) -> List[Tuple[int, float, Dict[str, Any]]]:
        """
        Reordena os resultados usando técnicas avançadas.
        
        Args:
            results: Resultados originais (idx, similaridade, metadados)
            query_embedding: Embedding da consulta
            
        Returns:
            Resultados reordenados
        """
        # Implementação básica: ajustar scores com base em heurísticas
        reranked_results = []
        
        for idx, similarity, metadata in results:
            # Obter o chunk
            chunk = self.index.get(idx)
            
            if not chunk:
                continue
                
            # Calcular score ajustado
            adjusted_score = similarity
            
            # Bônus para arquivos com símbolos relevantes
            if chunk.symbols:
                adjusted_score *= 1.1
                
            # Bônus para chunks menores (mais específicos)
            lines = chunk.end_line - chunk.start_line + 1
            if lines < 50:
                adjusted_score *= 1.05
                
            # Adicionar ao resultado
            reranked_results.append((idx, adjusted_score, metadata))
            
        # Ordenar por score ajustado
        reranked_results.sort(key=lambda x: x[1], reverse=True)
        
        return reranked_results
        
    def search_by_text(self, query: str, embedding_model, max_results: Optional[int] = None,
                      filter_criteria: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Busca snippets de código por texto.
        
        Args:
            query: Consulta em texto
            embedding_model: Modelo de embedding para converter a consulta
            max_results: Número máximo de resultados (opcional)
            filter_criteria: Critérios de filtro para os resultados (opcional)
            
        Returns:
            Lista de snippets de código relevantes
        """
        # Gerar embedding para a consulta
        query_embedding = embedding_model.embed_query(query)
        
        # Recuperar resultados
        return self.retrieve(query_embedding, max_results, filter_criteria)
        
    def search_similar_code(self, code: str, embedding_model, max_results: Optional[int] = None,
                           filter_criteria: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Busca snippets de código similares a um snippet fornecido.
        
        Args:
            code: Snippet de código
            embedding_model: Modelo de embedding para converter o código
            max_results: Número máximo de resultados (opcional)
            filter_criteria: Critérios de filtro para os resultados (opcional)
            
        Returns:
            Lista de snippets de código similares
        """
        # Gerar embedding para o código
        code_embedding = embedding_model.embed_code(code)
        
        # Recuperar resultados
        return self.retrieve(code_embedding, max_results, filter_criteria)
        
    def search_by_file(self, file_path: str, max_results: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Busca snippets de código de um arquivo específico.
        
        Args:
            file_path: Caminho do arquivo
            max_results: Número máximo de resultados (opcional)
            
        Returns:
            Lista de snippets de código do arquivo
        """
        # Filtrar por caminho de arquivo
        filter_criteria = {'file_path': file_path}
        
        # Buscar no índice sem usar embedding (busca exata)
        search_results = []
        
        # Percorrer todos os documentos no índice
        for i in range(self.index.size()):
            chunk = self.index.get(i)
            
            if chunk and chunk.file_path == file_path:
                search_results.append((i, 1.0, chunk.metadata))
                
        # Limitar ao número máximo de resultados
        if max_results is None:
            max_results = self.max_results
            
        results = search_results[:max_results]
        
        # Converter para o formato de resultado final
        final_results = []
        for idx, similarity, metadata in results:
            # Obter o chunk completo
            chunk = self.index.get(idx)
            
            if chunk:
                final_results.append({
                    'content': chunk.content,
                    'file_path': chunk.file_path,
                    'start_line': chunk.start_line,
                    'end_line': chunk.end_line,
                    'language': chunk.language,
                    'symbols': chunk.symbols,
                    'similarity': float(similarity),
                    'metadata': metadata
                })
                
        return final_results
