"""
Módulo especializado para correção de quebras de linha em textos gerados por modelos de linguagem.

Este módulo implementa algoritmos otimizados para detectar e corrigir problemas comuns
com quebras de linha em textos gerados por modelos de linguagem, especialmente quando
o modelo gera 'n' literal em vez de '\n' para representar quebras de linha.

Complexidade:
- Tempo: O(n) onde n é o tamanho do texto
- Espaço: O(n) para armazenar o texto processado
"""

import re
import os
import logging
from typing import List, Tuple, Dict, Set, Optional, Union, Pattern

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Constantes
COMMON_PREFIXES = {
    ')', '}', ']', '"', "'", ';', '.', ',', ':', '>', '<', '=', '+', '-', '*', '/', '\\', '|', '&', '^', '%', '#', '@', '!', '?'
}

COMMON_KEYWORDS = {
    'return', 'if', 'else', 'for', 'while', 'def', 'class', 'import', 'from', 'try', 'except', 'finally',
    'with', 'as', 'in', 'not', 'and', 'or', 'True', 'False', 'None', 'print', 'lambda', 'yield', 'break',
    'continue', 'pass', 'assert', 'raise', 'global', 'nonlocal', 'del', 'is', 'async', 'await'
}

# Padrões de regex pré-compilados para melhor performance
LITERAL_N_PATTERN = re.compile(r'([^\\\n])n([a-z])')
ESCAPED_N_PATTERN = re.compile(r'\\n')
MULTIPLE_NEWLINES_PATTERN = re.compile(r'\n{3,}')

def fix_literal_n_breaks(text: str) -> str:
    """
    Corrige quebras de linha com 'n' literal em vez de '\n'.

    Esta função implementa um algoritmo sofisticado para detectar e corrigir
    casos onde o modelo de linguagem gera 'n' literal em vez de '\n' para
    representar quebras de linha.

    Args:
        text (str): Texto a ser corrigido

    Returns:
        str: Texto com quebras de linha corrigidas

    Complexidade:
        - Tempo: O(n) onde n é o tamanho do texto
        - Espaço: O(n) para armazenar o texto processado
    """
    if not text or 'n' not in text:
        return text

    # Caso especial: Verificar se o texto contém padrões como "Linha 1nLinha 2nLinha 3"
    if re.search(r'Linha \d+n', text):
        # Este é um caso especial comum em textos gerados pelo modelo
        # Substituir "Linha 1nLinha 2" por "Linha 1\nLinha 2"
        result = re.sub(r'(Linha \d+)n(Linha \d+)', r'\1\n\2', text)

        # Se ainda houver 'n' após "Linha", substituir também
        result = re.sub(r'(Linha \d+)n([A-Za-z])', r'\1\n\2', result)

        return result

    # Abordagem 1: Substituição baseada em padrões comuns
    # Esta abordagem é mais rápida, mas pode não pegar todos os casos
    result = text

    # Substituir padrões como ")nimport" por ")\nimport"
    for prefix in COMMON_PREFIXES:
        pattern = f'({re.escape(prefix)})n([a-z])'
        result = re.sub(pattern, r'\1\n\2', result)

    # Substituir padrões como "returnnimport" por "return\nimport"
    for keyword in COMMON_KEYWORDS:
        pattern = f'({re.escape(keyword)})n([a-z])'
        result = re.sub(pattern, r'\1\n\2', result)

    # Substituir padrões como "123nimport" por "123\nimport"
    result = re.sub(r'(\d+)n([a-z])', r'\1\n\2', result)

    # Abordagem 2: Análise de contexto
    # Esta abordagem é mais precisa, mas mais lenta
    # Usamos esta abordagem apenas se a primeira não resolveu todos os problemas
    if 'n' in result and not re.search(r'\\n', result) and not '\n' in result:
        # Análise de contexto mais sofisticada
        chars = list(result)
        i = 0
        while i < len(chars) - 1:
            if chars[i] == 'n' and i > 0 and i < len(chars) - 1:
                prev_char = chars[i-1]
                next_char = chars[i+1]

                # Verificar se este 'n' provavelmente deveria ser uma quebra de linha
                if (prev_char in COMMON_PREFIXES or prev_char.isalnum() or prev_char.isspace()) and next_char.islower():
                    # Substituir 'n' por '\n'
                    chars[i] = '\n'
            i += 1

        result = ''.join(chars)

    return result

def fix_escaped_n_breaks(text: str) -> str:
    """
    Substitui sequências de escape '\\n' por quebras de linha reais '\n'.

    Args:
        text (str): Texto a ser corrigido

    Returns:
        str: Texto com quebras de linha corrigidas

    Complexidade:
        - Tempo: O(n) onde n é o tamanho do texto
        - Espaço: O(n) para armazenar o texto processado
    """
    if not text or '\\n' not in text:
        return text

    # Substituir '\\n' por '\n'
    return ESCAPED_N_PATTERN.sub('\n', text)

def normalize_line_breaks(text: str) -> str:
    """
    Normaliza quebras de linha para o formato universal '\n'.

    Args:
        text (str): Texto a ser normalizado

    Returns:
        str: Texto com quebras de linha normalizadas

    Complexidade:
        - Tempo: O(n) onde n é o tamanho do texto
        - Espaço: O(n) para armazenar o texto processado
    """
    if not text:
        return text

    # Normalizar todas as quebras de linha para '\n'
    return text.replace('\r\n', '\n').replace('\r', '\n')

def convert_to_system_line_breaks(text: str) -> str:
    """
    Converte quebras de linha para o formato do sistema operacional.

    Args:
        text (str): Texto a ser convertido

    Returns:
        str: Texto com quebras de linha no formato do sistema

    Complexidade:
        - Tempo: O(n) onde n é o tamanho do texto
        - Espaço: O(n) para armazenar o texto processado
    """
    if not text:
        return text

    # Primeiro normalizar para '\n'
    normalized = normalize_line_breaks(text)

    # Converter para o formato do sistema operacional
    if os.linesep != '\n':
        return normalized.replace('\n', os.linesep)

    return normalized

def fix_line_breaks(text: str, aggressive: bool = True) -> str:
    """
    Função principal que aplica todas as correções de quebras de linha.

    Esta função aplica uma série de transformações para corrigir problemas
    comuns com quebras de linha em textos gerados por modelos de linguagem.

    Args:
        text (str): Texto a ser corrigido
        aggressive (bool): Se True, aplica correções mais agressivas

    Returns:
        str: Texto com quebras de linha corrigidas

    Complexidade:
        - Tempo: O(n) onde n é o tamanho do texto
        - Espaço: O(n) para armazenar o texto processado
    """
    if not text:
        return text

    # Registrar estatísticas antes da correção
    original_n_count = text.count('n')
    original_newline_count = text.count('\n')
    original_escaped_n_count = text.count('\\n')

    # Aplicar correções em sequência
    result = text

    # 1. Normalizar quebras de linha existentes
    result = normalize_line_breaks(result)

    # 2. Substituir sequências de escape por quebras de linha reais
    result = fix_escaped_n_breaks(result)

    # 3. Corrigir 'n' literal que deveria ser '\n'
    result = fix_literal_n_breaks(result)

    # 4. Verificação especial para casos onde não há quebras de linha
    if '\n' not in result and 'n' in result and aggressive:
        # Tentar uma abordagem mais agressiva
        # Substituir todos os 'n' entre palavras por '\n'
        result = re.sub(r'([a-zA-Z0-9])n([a-zA-Z])', r'\1\n\2', result)

    # 5. Normalizar múltiplas quebras de linha consecutivas
    result = MULTIPLE_NEWLINES_PATTERN.sub('\n\n', result)

    # 6. Converter para o formato do sistema operacional
    result = convert_to_system_line_breaks(result)

    # Registrar estatísticas após a correção
    final_n_count = result.count('n')
    final_newline_count = result.count('\n')
    n_replaced = original_n_count - final_n_count

    logger.debug(f"Correção de quebras de linha: {n_replaced} 'n' substituídos por '\\n'")
    logger.debug(f"Quebras de linha antes: {original_newline_count}, depois: {final_newline_count}")

    return result

def debug_line_breaks(text: str) -> str:
    """
    Gera informações de debug sobre quebras de linha no texto.

    Args:
        text (str): Texto a ser analisado

    Returns:
        str: Informações de debug

    Complexidade:
        - Tempo: O(n) onde n é o tamanho do texto
        - Espaço: O(1) para armazenar as estatísticas
    """
    if not text:
        return "Texto vazio"

    # Contar diferentes tipos de quebras de linha
    n_count = text.count('n')
    newline_count = text.count('\n')
    carriage_return_count = text.count('\r')
    crlf_count = text.count('\r\n')
    escaped_n_count = text.count('\\n')

    # Verificar padrões suspeitos
    literal_n_matches = LITERAL_N_PATTERN.findall(text)

    # Gerar relatório
    report = [
        "Informações de debug sobre quebras de linha:",
        f"- Caracteres 'n': {n_count}",
        f"- Quebras de linha (\\n): {newline_count}",
        f"- Retornos de carro (\\r): {carriage_return_count - crlf_count}",
        f"- Quebras CRLF (\\r\\n): {crlf_count}",
        f"- Sequências de escape (\\\\n): {escaped_n_count}",
        f"- Padrões suspeitos ('?n?'): {len(literal_n_matches)}",
        f"- Quebra de linha do sistema: {repr(os.linesep)}",
        f"- Tamanho total do texto: {len(text)} caracteres"
    ]

    # Mostrar exemplos de padrões suspeitos
    if literal_n_matches:
        examples = literal_n_matches[:5]
        report.append("- Exemplos de padrões suspeitos:")
        for i, (before, after) in enumerate(examples, 1):
            report.append(f"  {i}. '{before}n{after}' (deveria ser '{before}\\n{after}'?)")

    return "\n".join(report)

# Função principal para uso em outros módulos
def process_text(text: str, aggressive: bool = True) -> str:
    """
    Processa um texto para corrigir problemas de quebras de linha.

    Esta é a função principal que deve ser chamada por outros módulos.

    Args:
        text (str): Texto a ser processado
        aggressive (bool): Se True, aplica correções mais agressivas

    Returns:
        str: Texto processado

    Complexidade:
        - Tempo: O(n) onde n é o tamanho do texto
        - Espaço: O(n) para armazenar o texto processado
    """
    return fix_line_breaks(text, aggressive)

# Função para uso em arquivos
def process_file(file_path: str, output_path: Optional[str] = None, aggressive: bool = True) -> str:
    """
    Processa um arquivo para corrigir problemas de quebras de linha.

    Args:
        file_path (str): Caminho do arquivo a ser processado
        output_path (str, optional): Caminho do arquivo de saída. Se None, sobrescreve o arquivo original.
        aggressive (bool): Se True, aplica correções mais agressivas

    Returns:
        str: Mensagem de resultado

    Complexidade:
        - Tempo: O(n) onde n é o tamanho do arquivo
        - Espaço: O(n) para armazenar o conteúdo do arquivo
    """
    try:
        # Ler o arquivo
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Processar o conteúdo
        processed_content = process_text(content, aggressive)

        # Determinar o caminho de saída
        out_path = output_path or file_path

        # Escrever o resultado
        with open(out_path, 'w', encoding='utf-8') as f:
            f.write(processed_content)

        return f"Arquivo processado com sucesso: {file_path} -> {out_path}"

    except Exception as e:
        return f"Erro ao processar arquivo: {str(e)}"

if __name__ == "__main__":
    # Código para teste do módulo
    import sys

    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        output_path = sys.argv[2] if len(sys.argv) > 2 else None

        result = process_file(file_path, output_path)
        print(result)
    else:
        print("Uso: python line_break_fixer.py <arquivo_entrada> [arquivo_saida]")
