"""
Módulo para processamento de prompts longos sem limitação de tokens.

Este módulo implementa técnicas avançadas para processar prompts que excedem
o limite de contexto do modelo, permitindo respostas de alta qualidade sem
truncamento de informações importantes.

Complexidade:
- Tempo: O(n) onde n é o tamanho do prompt
- Espaço: O(n) para armazenar o prompt e seus chunks
"""

import re
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Callable
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UnlimitedContextProcessor:
    """
    Processador de contexto ilimitado para modelos LLM.
    
    Esta classe implementa técnicas para processar prompts que excedem o limite
    de contexto do modelo, permitindo respostas de alta qualidade sem truncamento.
    
    Atributos:
        model_ctx_size (int): Tamanho do contexto do modelo
        overlap_ratio (float): Proporção de sobreposição entre chunks
        importance_threshold (float): Limiar para determinar seções importantes
    """
    
    def __init__(self, model_ctx_size: int = 4096, overlap_ratio: float = 0.1, importance_threshold: float = 0.7):
        """
        Inicializa o processador de contexto ilimitado.
        
        Args:
            model_ctx_size: Tamanho do contexto do modelo
            overlap_ratio: Proporção de sobreposição entre chunks (0.0 a 0.5)
            importance_threshold: Limiar para determinar seções importantes (0.0 a 1.0)
        """
        self.model_ctx_size = model_ctx_size
        self.overlap_ratio = max(0.0, min(0.5, overlap_ratio))  # Limitar entre 0.0 e 0.5
        self.importance_threshold = max(0.0, min(1.0, importance_threshold))  # Limitar entre 0.0 e 1.0
        
        # Padrões para identificar seções importantes
        self.important_patterns = [
            r"(?i)mensagem do usuário",
            r"(?i)instruções",
            r"(?i)importante",
            r"(?i)diretrizes",
            r"(?i)requisitos",
            r"(?i)objetivo",
            r"(?i)tarefa",
            r"(?i)problema",
            r"(?i)contexto",
            r"(?i)código",
            r"(?i)exemplo"
        ]
    
    def process_prompt(self, prompt: str, max_tokens_output: int = 1024, 
                      generate_fn: Callable[[str, int], str] = None) -> str:
        """
        Processa um prompt longo e gera uma resposta.
        
        Esta função divide o prompt em chunks se necessário, processa cada chunk
        e combina os resultados para gerar uma resposta completa.
        
        Args:
            prompt: O prompt completo
            max_tokens_output: Número máximo de tokens para a saída
            generate_fn: Função para gerar texto a partir de um prompt
            
        Returns:
            Resposta gerada
            
        Complexidade:
            - Tempo: O(n) onde n é o tamanho do prompt
            - Espaço: O(n) para armazenar o prompt e seus chunks
        """
        if generate_fn is None:
            logger.error("Função de geração não fornecida")
            return "Erro: Função de geração não fornecida"
        
        # Estimar o número de tokens no prompt (aproximadamente 1 token = 4 caracteres em inglês)
        # Esta é uma estimativa conservadora para evitar subestimar o número de tokens
        estimated_tokens = len(prompt) // 3
        
        # Se o prompt cabe no contexto do modelo, processá-lo diretamente
        if estimated_tokens <= self.model_ctx_size - max_tokens_output:
            logger.info(f"Prompt cabe no contexto do modelo ({estimated_tokens} tokens estimados)")
            return generate_fn(prompt, max_tokens_output)
        
        logger.info(f"Prompt excede o contexto do modelo ({estimated_tokens} tokens estimados). Usando processamento avançado.")
        
        # Dividir o prompt em chunks
        chunks = self._split_prompt_into_chunks(prompt)
        logger.info(f"Prompt dividido em {len(chunks)} chunks")
        
        # Se temos apenas um chunk (caso raro), processá-lo diretamente
        if len(chunks) == 1:
            return generate_fn(chunks[0], max_tokens_output)
        
        # Processar cada chunk e coletar as respostas
        responses = []
        for i, chunk in enumerate(chunks):
            logger.info(f"Processando chunk {i+1}/{len(chunks)} ({len(chunk)} caracteres)")
            
            # Adicionar contexto sobre o processamento em chunks
            if i == 0:
                # Primeiro chunk - adicionar informação sobre o processamento
                chunk_prompt = f"{chunk}\n\n[Nota: Esta é a primeira parte de um prompt longo. Forneça uma resposta parcial que será combinada com as respostas das partes subsequentes.]"
            elif i == len(chunks) - 1:
                # Último chunk - adicionar informação sobre o processamento
                chunk_prompt = f"{chunk}\n\n[Nota: Esta é a última parte de um prompt longo. Forneça uma resposta final que complemente as respostas anteriores.]"
            else:
                # Chunks intermediários - adicionar informação sobre o processamento
                chunk_prompt = f"{chunk}\n\n[Nota: Esta é a parte {i+1} de um prompt longo. Forneça uma resposta parcial que será combinada com as respostas das outras partes.]"
            
            # Gerar resposta para o chunk
            chunk_response = generate_fn(chunk_prompt, max_tokens_output // len(chunks))
            responses.append(chunk_response)
        
        # Combinar as respostas
        combined_response = self._combine_responses(responses)
        
        return combined_response
    
    def process_chat(self, messages: List[Dict[str, str]], max_tokens_output: int = 1024,
                    chat_fn: Callable[[List[Dict[str, str]], int], str] = None) -> str:
        """
        Processa mensagens de chat longas e gera uma resposta.
        
        Esta função processa mensagens de chat que excedem o limite de contexto
        do modelo, mantendo as mensagens mais importantes.
        
        Args:
            messages: Lista de mensagens de chat
            max_tokens_output: Número máximo de tokens para a saída
            chat_fn: Função para gerar texto a partir de mensagens de chat
            
        Returns:
            Resposta gerada
            
        Complexidade:
            - Tempo: O(n) onde n é o número total de tokens nas mensagens
            - Espaço: O(n) para armazenar as mensagens
        """
        if chat_fn is None:
            logger.error("Função de chat não fornecida")
            return "Erro: Função de chat não fornecida"
        
        # Estimar o número total de tokens nas mensagens
        total_tokens = sum(len(msg.get("content", "")) // 3 for msg in messages)
        
        # Se as mensagens cabem no contexto do modelo, processá-las diretamente
        if total_tokens <= self.model_ctx_size - max_tokens_output:
            logger.info(f"Mensagens cabem no contexto do modelo ({total_tokens} tokens estimados)")
            return chat_fn(messages, max_tokens_output)
        
        logger.info(f"Mensagens excedem o contexto do modelo ({total_tokens} tokens estimados). Usando processamento avançado.")
        
        # Identificar mensagens importantes
        system_message = None
        user_messages = []
        assistant_messages = []
        
        for msg in messages:
            role = msg.get("role", "")
            if role == "system":
                system_message = msg
            elif role == "user":
                user_messages.append(msg)
            elif role == "assistant":
                assistant_messages.append(msg)
        
        # Garantir que temos a última mensagem do usuário
        last_user_message = user_messages[-1] if user_messages else None
        
        # Criar conjuntos de mensagens para processamento
        message_sets = []
        
        # Conjunto 1: Sistema + Última mensagem do usuário
        if system_message and last_user_message:
            message_sets.append([system_message, last_user_message])
        elif last_user_message:
            message_sets.append([last_user_message])
        
        # Conjunto 2: Sistema + Histórico recente + Última mensagem do usuário
        if len(user_messages) > 1 and len(assistant_messages) > 0:
            recent_history = []
            # Adicionar pares de mensagens (usuário + assistente) mais recentes
            for i in range(min(3, min(len(user_messages)-1, len(assistant_messages)))):
                recent_history.append(user_messages[-(i+2)])
                recent_history.append(assistant_messages[-(i+1)])
            
            if system_message:
                message_set = [system_message] + recent_history + [last_user_message]
            else:
                message_set = recent_history + [last_user_message]
            
            message_sets.append(message_set)
        
        # Processar cada conjunto de mensagens e coletar as respostas
        responses = []
        for i, message_set in enumerate(message_sets):
            logger.info(f"Processando conjunto de mensagens {i+1}/{len(message_sets)} ({len(message_set)} mensagens)")
            
            # Gerar resposta para o conjunto de mensagens
            response = chat_fn(message_set, max_tokens_output // len(message_sets))
            responses.append(response)
        
        # Combinar as respostas
        combined_response = self._combine_responses(responses)
        
        return combined_response
    
    def _split_prompt_into_chunks(self, prompt: str) -> List[str]:
        """
        Divide um prompt longo em chunks processáveis.
        
        Esta função divide o prompt em chunks que cabem no contexto do modelo,
        garantindo que informações importantes não sejam perdidas.
        
        Args:
            prompt: O prompt completo
            
        Returns:
            Lista de chunks do prompt
            
        Complexidade:
            - Tempo: O(n) onde n é o tamanho do prompt
            - Espaço: O(n) para armazenar os chunks
        """
        # Estimar o número máximo de caracteres por chunk
        # Usamos um fator de segurança para evitar exceder o limite de tokens
        safety_factor = 0.8
        max_chars_per_chunk = int((self.model_ctx_size * safety_factor) * 3)  # ~3 caracteres por token
        
        # Se o prompt é menor que o tamanho máximo, retorná-lo como um único chunk
        if len(prompt) <= max_chars_per_chunk:
            return [prompt]
        
        # Dividir o prompt em parágrafos
        paragraphs = re.split(r'\n\s*\n', prompt)
        
        # Calcular a importância de cada parágrafo
        paragraph_importance = self._calculate_paragraph_importance(paragraphs)
        
        # Criar chunks com base na importância e no tamanho máximo
        chunks = []
        current_chunk = ""
        current_chunk_size = 0
        overlap_size = int(max_chars_per_chunk * self.overlap_ratio)
        
        # Identificar parágrafos críticos (que devem estar em todos os chunks)
        critical_paragraphs = []
        for i, importance in enumerate(paragraph_importance):
            if importance > self.importance_threshold:
                critical_paragraphs.append(paragraphs[i])
        
        # Texto crítico a ser incluído em cada chunk
        critical_text = "\n\n".join(critical_paragraphs)
        critical_size = len(critical_text)
        
        # Ajustar o tamanho máximo do chunk para acomodar o texto crítico
        adjusted_max_size = max_chars_per_chunk - critical_size
        
        # Se o texto crítico é muito grande, dividir em múltiplos chunks
        if critical_size > max_chars_per_chunk * 0.5:
            logger.warning(f"Texto crítico muito grande ({critical_size} caracteres). Dividindo em múltiplos chunks.")
            # Neste caso, não podemos garantir que todo o texto crítico estará em cada chunk
            # Vamos dividir o texto crítico em partes menores
            critical_paragraphs = []
            adjusted_max_size = max_chars_per_chunk
        
        # Adicionar parágrafos aos chunks
        for i, paragraph in enumerate(paragraphs):
            paragraph_size = len(paragraph)
            
            # Se o parágrafo é crítico, pular (será adicionado depois)
            if paragraph in critical_paragraphs:
                continue
            
            # Se o parágrafo é muito grande, dividi-lo em partes menores
            if paragraph_size > adjusted_max_size:
                # Dividir o parágrafo em sentenças
                sentences = re.split(r'(?<=[.!?])\s+', paragraph)
                for sentence in sentences:
                    sentence_size = len(sentence)
                    
                    # Se a sentença é muito grande, dividi-la em partes menores
                    if sentence_size > adjusted_max_size:
                        # Dividir a sentença em partes menores
                        parts = [sentence[i:i+adjusted_max_size] for i in range(0, len(sentence), adjusted_max_size)]
                        for part in parts:
                            if current_chunk_size + len(part) + 2 > adjusted_max_size:
                                # Adicionar texto crítico ao chunk atual
                                if critical_paragraphs:
                                    current_chunk = critical_text + "\n\n" + current_chunk
                                
                                chunks.append(current_chunk)
                                current_chunk = ""
                                current_chunk_size = 0
                            
                            if current_chunk:
                                current_chunk += "\n\n"
                                current_chunk_size += 2
                            
                            current_chunk += part
                            current_chunk_size += len(part)
                    else:
                        if current_chunk_size + sentence_size + 2 > adjusted_max_size:
                            # Adicionar texto crítico ao chunk atual
                            if critical_paragraphs:
                                current_chunk = critical_text + "\n\n" + current_chunk
                            
                            chunks.append(current_chunk)
                            
                            # Iniciar novo chunk com sobreposição
                            overlap_text = current_chunk[-overlap_size:] if overlap_size > 0 else ""
                            current_chunk = overlap_text
                            current_chunk_size = len(overlap_text)
                        
                        if current_chunk:
                            current_chunk += "\n\n"
                            current_chunk_size += 2
                        
                        current_chunk += sentence
                        current_chunk_size += sentence_size
            else:
                if current_chunk_size + paragraph_size + 2 > adjusted_max_size:
                    # Adicionar texto crítico ao chunk atual
                    if critical_paragraphs:
                        current_chunk = critical_text + "\n\n" + current_chunk
                    
                    chunks.append(current_chunk)
                    
                    # Iniciar novo chunk com sobreposição
                    overlap_text = current_chunk[-overlap_size:] if overlap_size > 0 else ""
                    current_chunk = overlap_text
                    current_chunk_size = len(overlap_text)
                
                if current_chunk:
                    current_chunk += "\n\n"
                    current_chunk_size += 2
                
                current_chunk += paragraph
                current_chunk_size += paragraph_size
        
        # Adicionar o último chunk se não estiver vazio
        if current_chunk:
            # Adicionar texto crítico ao último chunk
            if critical_paragraphs:
                current_chunk = critical_text + "\n\n" + current_chunk
            
            chunks.append(current_chunk)
        
        return chunks
    
    def _calculate_paragraph_importance(self, paragraphs: List[str]) -> List[float]:
        """
        Calcula a importância de cada parágrafo com base em padrões predefinidos.
        
        Args:
            paragraphs: Lista de parágrafos
            
        Returns:
            Lista de valores de importância (0.0 a 1.0) para cada parágrafo
            
        Complexidade:
            - Tempo: O(n*m) onde n é o número de parágrafos e m é o número de padrões
            - Espaço: O(n) para armazenar os valores de importância
        """
        importance_scores = []
        
        for paragraph in paragraphs:
            # Inicializar com importância base
            importance = 0.1
            
            # Verificar padrões importantes
            for pattern in self.important_patterns:
                if re.search(pattern, paragraph):
                    importance += 0.2
                    # Limitar a importância máxima a 1.0
                    if importance >= 1.0:
                        importance = 1.0
                        break
            
            # Aumentar importância para parágrafos curtos (geralmente são títulos ou instruções)
            if len(paragraph) < 100:
                importance += 0.1
            
            # Aumentar importância para parágrafos com formatação especial
            if re.search(r'[*_`#]', paragraph):
                importance += 0.1
            
            # Aumentar importância para parágrafos com números (geralmente são listas ou passos)
            if re.search(r'\d+\.', paragraph):
                importance += 0.1
            
            importance_scores.append(min(1.0, importance))
        
        return importance_scores
    
    def _combine_responses(self, responses: List[str]) -> str:
        """
        Combina múltiplas respostas em uma única resposta coerente.
        
        Args:
            responses: Lista de respostas a serem combinadas
            
        Returns:
            Resposta combinada
            
        Complexidade:
            - Tempo: O(n) onde n é o tamanho total das respostas
            - Espaço: O(n) para armazenar a resposta combinada
        """
        if not responses:
            return ""
        
        if len(responses) == 1:
            return responses[0]
        
        # Remover notas sobre processamento em chunks
        cleaned_responses = []
        for response in responses:
            # Remover notas sobre processamento em chunks
            cleaned = re.sub(r'\[Nota:.*?\]', '', response)
            cleaned_responses.append(cleaned)
        
        # Combinar as respostas
        combined = cleaned_responses[0]
        
        for response in cleaned_responses[1:]:
            # Evitar repetição de conteúdo
            if response in combined:
                continue
            
            # Verificar se há sobreposição significativa no final de combined e início de response
            overlap_found = False
            for overlap_size in range(min(100, len(combined)), 10, -10):
                end_of_combined = combined[-overlap_size:]
                if end_of_combined in response[:overlap_size*2]:
                    # Encontrar onde termina a sobreposição
                    overlap_end = response.find(end_of_combined) + overlap_size
                    # Adicionar apenas o conteúdo após a sobreposição
                    combined += response[overlap_end:]
                    overlap_found = True
                    break
            
            if not overlap_found:
                # Se não houver sobreposição, adicionar uma transição e a resposta completa
                combined += "\n\n" + response
        
        return combined
