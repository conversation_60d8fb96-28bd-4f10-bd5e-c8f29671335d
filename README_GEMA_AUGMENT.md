# Gema Agent (Estilo Augment)

Este é um agente de IA que funciona de forma similar ao Augment Agent, processando instruções em linguagem natural e executando tarefas de codificação. O agente exibe caixas de diálogo durante o processo, como "created file", "read file", "copy", "terminal", "edited file", "web", "get", etc.

## Configuração

Para configurar o ambiente do Gema Agent, execute o script `setup_gema.bat`. Este script irá:

1. Verificar se o Python está instalado
2. Instalar as dependências necessárias
3. Criar os diretórios necessários
4. Verificar se o modelo de linguagem está disponível

```
setup_gema.bat
```

## Execução

Para executar o Gema Agent no estilo Augment, use o script `gema_augment.bat`:

```
gema_augment
```

Isso iniciará o agente em modo interativo, onde você pode digitar instruções em linguagem natural.

Para processar uma instrução específica, você pode passá-la como argumento:

```
gema_augment "Crie um arquivo chamado exemplo.txt com o conteúdo 'Olá, mundo!'"
```

## Exemplos de Instruções

Aqui estão alguns exemplos de instruções que você pode dar ao Gema Agent:

1. **Criar um arquivo de texto:**
   ```
   Crie um arquivo chamado notas.txt com o conteúdo 'Estas são minhas notas importantes'
   ```

2. **Criar um script Python:**
   ```
   Crie um script Python chamado calculadora.py que tenha funções para somar, subtrair, multiplicar e dividir dois números
   ```

3. **Ler um arquivo:**
   ```
   Leia o conteúdo do arquivo exemplo.txt
   ```

4. **Listar arquivos:**
   ```
   Liste todos os arquivos Python no diretório atual
   ```

5. **Executar um comando do sistema:**
   ```
   Execute o comando 'dir' para listar os arquivos no diretório atual
   ```

6. **Copiar um arquivo:**
   ```
   Copie o arquivo exemplo.txt para backup.txt
   ```

## Caixas de Diálogo

O Gema Agent exibe caixas de diálogo durante o processo de codificação:

- `[CREATED FILE: nome_do_arquivo]` - Quando um arquivo é criado
- `[READ FILE: nome_do_arquivo]` - Quando um arquivo é lido
- `[COPIED FILE: origem -> destino]` - Quando um arquivo é copiado
- `[TERMINAL: comando]` - Quando um comando é executado
- `[EDITED FILE: nome_do_arquivo]` - Quando um arquivo é editado
- `[WEB: consulta]` - Quando uma busca na web é realizada
- `[GET: informação]` - Quando informações são obtidas

## Ferramentas Disponíveis

O Gema Agent inclui um conjunto completo de ferramentas para manipulação de arquivos, codificação, execução de comandos e muito mais:

### Ferramentas de Arquivo
- `write_file` - Cria ou sobrescreve um arquivo com suporte a Unicode
- `read_file` - Lê o conteúdo de um arquivo com detecção automática de codificação
- `list_files` - Lista arquivos em um diretório
- `remove_file` - Remove um arquivo
- `copy_file` - Copia um arquivo

### Ferramentas de Codificação
- `encode_base64` - Codifica texto em base64
- `decode_base64` - Decodifica texto de base64
- `debug_string` - Retorna informações de debug sobre uma string
- `write_file_unicode` - Escreve conteúdo em um arquivo com suporte a BOM
- `read_file_unicode` - Lê o conteúdo de um arquivo com informações detalhadas de codificação
- `normalize_unicode` - Normaliza um texto Unicode para uma forma específica
- `detect_encoding` - Detecta a codificação de um texto em bytes
- `convert_encoding` - Converte um texto de uma codificação para outra
- `get_supported_encodings` - Retorna uma lista de codificações suportadas pelo sistema

### Ferramentas de Sistema
- `execute_command` - Executa um comando do sistema

### Ferramentas de Busca
- `web_search` - Busca informações na web

## Requisitos

- Python 3.8 ou superior
- Dependências: colorama, chardet, sentence-transformers, unicodedata2, llama-cpp-python
- Modelo de linguagem: DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf

## Melhorias Recentes

O Gema Agent foi recentemente atualizado com as seguintes melhorias:

1. **Suporte Robusto a Codificação Unicode:**
   - Implementação completa de ferramentas de codificação universal
   - Detecção automática de codificação de arquivos
   - Normalização Unicode para garantir compatibilidade

2. **Processamento Melhorado de Argumentos:**
   - Parser robusto para argumentos de função que lida corretamente com strings contendo aspas e caracteres especiais
   - Tratamento adequado de valores booleanos e numéricos

3. **Verificação de Integridade de Arquivos:**
   - Verificação automática do conteúdo após a escrita de arquivos
   - Diagnóstico detalhado em caso de problemas

4. **Prompt Aprimorado para o Modelo:**
   - Instruções claras sobre como usar as ferramentas
   - Exemplos específicos para garantir o formato correto das chamadas de ferramentas
   - Orientações sobre como lidar com caracteres especiais

## Solução de Problemas

Se você encontrar problemas ao executar o Gema Agent:

1. **Erro de importação de módulos:**
   - Execute `setup_gema.bat` para instalar as dependências necessárias

2. **Erro de codificação:**
   - Certifique-se de que os arquivos estão sendo salvos com codificação UTF-8
   - Use a ferramenta `debug_string` para diagnosticar problemas de codificação
   - Considere usar `normalize_unicode` para normalizar textos com caracteres especiais

3. **Modelo de linguagem não encontrado:**
   - Verifique se o caminho para o modelo está correto no arquivo `gema_augment_style.py`
   - Baixe o modelo se necessário

4. **Problemas com caracteres especiais:**
   - Use a ferramenta `write_file` com o parâmetro `encoding='utf-8'`
   - Para casos mais complexos, use `write_file_unicode` com o parâmetro `bom=True`

5. **Problemas com o processamento de argumentos:**
   - Certifique-se de que as strings estão corretamente entre aspas simples ou duplas
   - Evite usar caracteres especiais como delimitadores em argumentos
