{
  "n_gpu_layers": 32,
  "main_gpu": 0,
  "tensor_split": "8",
  "n_threads": 6,
  "use_mlock": true,
  "offload_kqv": true,
  "vulkan_compatible": true
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 4096,
            "n_gpu_layers": 35,
            "main_gpu": 0,
            "tensor_split": "8",
            "n_threads": 8,
            "use_mlock": true,
            "offload_kqv": true,
            "vulkan_compatible": true,
            "clblast_dir": "C:\\Users\\<USER>\\VulkanSDK"
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache",
        "learning": {
            "enabled": true,
            "max_examples": 100,
            "similarity_threshold": 0.5,
            "feedback_threshold": 4,
            "cache_dir": "./learning_cache"
        }
    }
}
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf",
            "n_ctx": 16384,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096,
            "unlimited_context": false,
            "overlap_ratio": 0.1,
            "importance_threshold": 0.7
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results":
