# Augment Agent Auto - Versão Unificada

Este projeto é uma implementação local do Augment Agent Auto, similar ao disponível em [augmentcode.com](https://www.augmentcode.com). Ele integra um motor de contexto avançado, um modelo de linguagem local (LLM) e um conjunto de ferramentas para criar um assistente de programação poderoso.

## Características

- **Motor de Contexto**: Indexa e compreende seu código para fornecer respostas contextualizadas
- **Modelo de Linguagem Local**: Utiliza o modelo Claude 3.7 Sonnet Reasoning Gemma3 12B localmente
- **Ferramentas Integradas**: Manipulação de arquivos, busca na web, execução de comandos e mais
- **Interface Dual**: Escolha entre interface de linha de comando (CLI) ou interface web
- **Sistema de Aprendizado**: Melhora com o tempo baseado nas interações anteriores

## Requisitos

- Python 3.8 ou superior
- Modelo Claude 3.7 Sonnet Reasoning Gemma3 12B (ou outro modelo compatível com llama-cpp-python)
- Pelo menos 8GB de RAM (16GB recomendado)
- Espaço em disco para o modelo e índices (aproximadamente 10GB)

## Instalação

1. Clone ou baixe este repositório
2. Execute o script `executar_augment_auto.bat` para configurar o ambiente e iniciar o agente

O script irá:
- Criar um ambiente virtual Python
- Instalar as dependências necessárias
- Verificar se o modelo está disponível
- Iniciar o Augment Agent Auto com a interface escolhida

## Uso

### Iniciar o Agente

```bash
# Usando o script de execução (recomendado)
executar_augment_auto.bat

# Ou diretamente via Python
python cpunionagenteauto.py --ui cli --codebase "caminho/para/seu/projeto"
```

### Parâmetros de Linha de Comando

- `--ui`: Tipo de interface (`cli` ou `web`)
- `--codebase`: Caminho para o codebase a ser indexado
- `--model`: Caminho para o modelo LLM
- `--config`: Caminho para um arquivo de configuração personalizado
- `--log-level`: Nível de log (`DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`)

### Configuração Personalizada

Você pode criar um arquivo `config.json` personalizado com a seguinte estrutura:

```json
{
    "llm": {
        "type": "local",
        "local": {
            "model_path": "caminho/para/seu/modelo.gguf",
            "n_ctx": 8192,
            "n_batch": 512,
            "n_gpu_layers": 0,
            "temperature": 0.7,
            "max_tokens": 4096
        }
    },
    "context_engine": {
        "embedding_dim": 768,
        "model_type": "code",
        "cache_dir": "./context_engine_cache",
        "max_results": 5
    },
    "agent": {
        "max_context_results": 5,
        "max_conversation_history": 10,
        "cache_dir": "./agent_cache"
    },
    "ui": {
        "type": "cli",
        "web": {
            "host": "127.0.0.1",
            "port": 5000,
            "open_browser": true
        }
    }
}
```

## Estrutura do Projeto

- `cpunionagenteauto.py`: Script principal unificado
- `executar_augment_auto.bat`: Script para configurar e executar o agente
- `agent/`: Módulos do agente principal
- `context_engine/`: Motor de contexto para indexação e recuperação de código
- `llm_integration/`: Integração com modelos de linguagem locais
- `tools/`: Ferramentas disponíveis para o agente
- `ui/`: Interfaces de usuário (CLI e Web)
- `prompts/`: Prompts do sistema para o LLM

## Ferramentas Disponíveis

O Augment Agent Auto inclui as seguintes categorias de ferramentas:

- **Ferramentas de Código**: Análise, formatação e geração de código
- **Ferramentas de Arquivo**: Leitura, escrita e manipulação de arquivos
- **Ferramentas de Busca**: Busca na web e recuperação de informações
- **Ferramentas de Processo**: Execução de comandos e gerenciamento de processos

## Personalização

### Modelo de Linguagem

Por padrão, o agente usa o modelo Claude 3.7 Sonnet Reasoning Gemma3 12B. Você pode usar qualquer modelo compatível com a biblioteca llama-cpp-python, especificando o caminho com o parâmetro `--model`.

### Prompt do Sistema

O prompt do sistema está localizado em `prompts/master_prompt.txt`. Você pode modificá-lo para personalizar o comportamento do agente.

## Solução de Problemas

### Modelo não encontrado

Se o modelo não for encontrado no caminho padrão, você pode especificar um caminho diferente usando o parâmetro `--model`.

### Erro de memória

Se você encontrar erros de memória, tente:
1. Reduzir o valor de `n_ctx` na configuração
2. Usar um modelo menor ou mais quantizado
3. Fechar outros aplicativos que consomem muita memória

### Problemas de indexação

Se a indexação do codebase falhar, verifique:
1. Se o caminho está correto
2. Se você tem permissões de leitura para os arquivos
3. Se o codebase não é muito grande (considere indexar apenas partes relevantes)

## Contribuição

Contribuições são bem-vindas! Sinta-se à vontade para abrir issues ou pull requests.

## Licença

Este projeto é licenciado sob a licença MIT.
