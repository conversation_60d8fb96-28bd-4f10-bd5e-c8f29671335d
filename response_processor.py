"""
Processador de Respostas para o Gema Agent.

Este módulo implementa o processamento de respostas do LLM para formatá-las
no estilo do Augment Agent Auto, com etapas claras e caixas de diálogo.

Complexidade:
- Tempo: O(n) onde n é o tamanho da resposta
- Espaço: O(n) para armazenar a resposta processada
"""

import re
import logging
import colorama
from typing import Dict, List, Any, Optional, Tuple, Set

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ResponseProcessor:
    """
    Processador de respostas do LLM para o Gema Agent.
    
    Esta classe implementa o processamento de respostas do LLM para formatá-las
    no estilo do Augment Agent Auto, com etapas claras e caixas de diálogo.
    
    Atributos:
        programming_steps (List[str]): Lista de etapas de programação
        tool_patterns (Dict[str, str]): Padrões para detectar chamadas de ferramentas
        dialog_formats (Dict[str, str]): Formatos para caixas de diálogo
    """
    
    def __init__(self):
        """
        Inicializa o processador de respostas.
        """
        # Etapas de programação no estilo Augment Agent Auto
        self.programming_steps = [
            "Análise do Problema",
            "Planejamento da Solução",
            "Implementação",
            "Testes",
            "Documentação",
            "Conclusão"
        ]
        
        # Padrões para detectar chamadas de ferramentas
        self.tool_patterns = {
            # Padrão para {{tool.name(args)}}
            "double_braces": r'\{\{tool\.(\w+)\(([^{}]*(?:\{[^{}]*\}[^{}]*)*)\)\}\}',
            # Padrão para {tool.name(args)}
            "single_braces": r'\{tool\.(\w+)\(([^{}]*(?:\{[^{}]*\}[^{}]*)*)\)\}',
            # Padrão para tool.name(args)
            "no_braces": r'tool\.(\w+)\(([^()]*(?:\([^()]*\)[^()]*)*)\)'
        }
        
        # Formatos para caixas de diálogo
        self.dialog_formats = {
            "read_file": "Read file\n{}\n{}",
            "read_lines": "Read lines {}-{}\n{}\n{}",
            "write_file": "Create file\n{}\n{}",
            "edit_file": "Edit file\n{}\n{}",
            "execute_command": "Terminal\n$ {}",
            "web_search": "Web search\n{}",
            "analyze_code": "Analyze code\n{}\n{}"
        }
        
        # Mapeamento de nomes de ferramentas para tipos de diálogo
        self.tool_to_dialog = {
            "read_file": "read_file",
            "list_files": "read_file",
            "write_file": "write_file",
            "write_file_unicode": "write_file",
            "execute_command": "execute_command",
            "web_search": "web_search"
        }
        
        # Padrão para detectar tags de código
        self.code_tag_pattern = r'```(\w*)\n(.*?)```'
        
        # Padrão para detectar etapas de programação
        self.step_pattern = r'(?:^|\n)#+\s*({})'.format('|'.join(self.programming_steps))
        
        # Padrão para detectar tags XML de código do Augment Agent
        self.xml_code_pattern = r'<augment_code_snippet\s+path="([^"]+)"\s+mode="([^"]+)">\s*```+(\w*)\s*(.*?)\s*```+\s*</augment_code_snippet>'
    
    def process_response(self, response: str, tools_output: Dict[str, Any] = None) -> str:
        """
        Processa a resposta do LLM para formatá-la no estilo do Augment Agent Auto.
        
        Args:
            response: Resposta do LLM
            tools_output: Saída das ferramentas executadas (opcional)
            
        Returns:
            Resposta processada
            
        Complexidade:
            - Tempo: O(n) onde n é o tamanho da resposta
            - Espaço: O(n) para armazenar a resposta processada
        """
        # Verificar se a resposta já está no formato do Augment Agent Auto
        if self._is_augment_style(response):
            logger.info("Resposta já está no formato do Augment Agent Auto")
            return response
        
        # Adicionar etapas de programação se não estiverem presentes
        response = self._add_programming_steps(response)
        
        # Processar chamadas de ferramentas
        response = self._process_tool_calls(response, tools_output)
        
        # Formatar blocos de código com tags XML
        response = self._format_code_blocks(response)
        
        # Adicionar conclusão se não estiver presente
        if "## Conclusão" not in response:
            response = self._add_conclusion(response)
        
        return response
    
    def _is_augment_style(self, response: str) -> bool:
        """
        Verifica se a resposta já está no formato do Augment Agent Auto.
        
        Args:
            response: Resposta do LLM
            
        Returns:
            True se a resposta já estiver no formato do Augment Agent Auto
        """
        # Verificar se há pelo menos 3 etapas de programação
        steps_found = re.findall(self.step_pattern, response, re.IGNORECASE)
        if len(steps_found) >= 3:
            return True
        
        # Verificar se há pelo menos uma caixa de diálogo
        for dialog_format in self.dialog_formats.values():
            pattern = dialog_format.format('.*', '.*').replace('\n', '\\s*').replace('$', '\\$')
            if re.search(pattern, response, re.DOTALL):
                return True
        
        # Verificar se há pelo menos uma tag XML de código
        if re.search(self.xml_code_pattern, response, re.DOTALL):
            return True
        
        return False
    
    def _add_programming_steps(self, response: str) -> str:
        """
        Adiciona etapas de programação à resposta se não estiverem presentes.
        
        Args:
            response: Resposta do LLM
            
        Returns:
            Resposta com etapas de programação
        """
        # Verificar quais etapas já estão presentes
        steps_found = set()
        for match in re.finditer(self.step_pattern, response, re.IGNORECASE):
            step = match.group(1)
            steps_found.add(step)
        
        # Se todas as etapas já estiverem presentes, retornar a resposta original
        if len(steps_found) >= len(self.programming_steps):
            return response
        
        # Adicionar etapas faltantes
        structured_response = ""
        
        # Adicionar título se não houver
        if not response.startswith("#"):
            structured_response += "# Resposta\n\n"
        
        # Adicionar etapas na ordem correta
        for step in self.programming_steps:
            if step in steps_found:
                # Extrair a seção existente
                pattern = r'(?:^|\n)#+\s*{}\s*\n(.*?)(?=(?:^|\n)#+\s*|$)'.format(re.escape(step))
                match = re.search(pattern, response, re.DOTALL)
                if match:
                    section_content = match.group(1).strip()
                    structured_response += f"## {step}\n\n{section_content}\n\n"
            else:
                # Adicionar etapa faltante com conteúdo padrão
                structured_response += f"## {step}\n\n"
                if step == "Análise do Problema":
                    structured_response += "Analisando o problema para entender os requisitos e objetivos.\n\n"
                elif step == "Planejamento da Solução":
                    structured_response += "Planejando a abordagem para resolver o problema de forma eficiente.\n\n"
                elif step == "Implementação":
                    structured_response += "Implementando a solução conforme o planejamento.\n\n"
                elif step == "Testes":
                    structured_response += "Verificando se a solução funciona corretamente.\n\n"
                elif step == "Documentação":
                    structured_response += "Documentando a solução para facilitar o entendimento e manutenção.\n\n"
                elif step == "Conclusão":
                    structured_response += "Resumindo o que foi feito e os resultados obtidos.\n\n"
        
        # Adicionar conteúdo original que não se encaixa em nenhuma etapa
        original_content = response
        for step in steps_found:
            pattern = r'(?:^|\n)#+\s*{}\s*\n(.*?)(?=(?:^|\n)#+\s*|$)'.format(re.escape(step))
            original_content = re.sub(pattern, '', original_content, flags=re.DOTALL)
        
        original_content = original_content.strip()
        if original_content:
            # Tentar identificar onde o conteúdo original deve ser inserido
            if "## Implementação" in structured_response and "## Testes" in structured_response:
                # Inserir entre Implementação e Testes
                structured_response = structured_response.replace("## Testes", original_content + "\n\n## Testes")
            else:
                # Adicionar ao final
                structured_response += original_content + "\n\n"
        
        return structured_response
    
    def _process_tool_calls(self, response: str, tools_output: Dict[str, Any] = None) -> str:
        """
        Processa chamadas de ferramentas na resposta.
        
        Args:
            response: Resposta do LLM
            tools_output: Saída das ferramentas executadas (opcional)
            
        Returns:
            Resposta com chamadas de ferramentas processadas
        """
        processed_response = response
        
        # Processar cada padrão de chamada de ferramenta
        for pattern_name, pattern in self.tool_patterns.items():
            # Encontrar todas as chamadas de ferramentas
            for match in re.finditer(pattern, response):
                tool_name = match.group(1)
                tool_args = match.group(2)
                
                # Extrair argumentos
                args_dict = self._parse_tool_args(tool_args)
                
                # Criar caixa de diálogo
                dialog = self._create_tool_dialog(tool_name, args_dict)
                
                if dialog:
                    # Substituir a chamada de ferramenta pela caixa de diálogo
                    full_match = match.group(0)
                    processed_response = processed_response.replace(full_match, f"`{dialog}`")
        
        return processed_response
    
    def _parse_tool_args(self, args_str: str) -> Dict[str, str]:
        """
        Analisa os argumentos de uma chamada de ferramenta.
        
        Args:
            args_str: String de argumentos
            
        Returns:
            Dicionário de argumentos
        """
        args_dict = {}
        
        # Padrão para extrair argumentos no formato name='value' ou name="value"
        pattern = r'(\w+)\s*=\s*(?:\'([^\']*)\'|"([^"]*)")'
        
        for match in re.finditer(pattern, args_str):
            arg_name = match.group(1)
            # O valor pode estar no grupo 2 (aspas simples) ou 3 (aspas duplas)
            arg_value = match.group(2) if match.group(2) is not None else match.group(3)
            args_dict[arg_name] = arg_value
        
        return args_dict
    
    def _create_tool_dialog(self, tool_name: str, args: Dict[str, str]) -> Optional[str]:
        """
        Cria uma caixa de diálogo para uma chamada de ferramenta.
        
        Args:
            tool_name: Nome da ferramenta
            args: Argumentos da ferramenta
            
        Returns:
            Caixa de diálogo ou None se não for possível criar
        """
        # Verificar se a ferramenta tem um formato de diálogo
        dialog_type = self.tool_to_dialog.get(tool_name)
        if not dialog_type:
            return None
        
        # Criar caixa de diálogo com base no tipo
        if dialog_type == "read_file":
            path = args.get("path", "")
            return self.dialog_formats["read_file"].format(path, path)
        elif dialog_type == "write_file":
            path = args.get("path", "")
            return self.dialog_formats["write_file"].format(path, path)
        elif dialog_type == "execute_command":
            command = args.get("command", "")
            return self.dialog_formats["execute_command"].format(command)
        elif dialog_type == "web_search":
            query = args.get("query", "")
            return self.dialog_formats["web_search"].format(query)
        
        return None
    
    def _format_code_blocks(self, response: str) -> str:
        """
        Formata blocos de código com tags XML.
        
        Args:
            response: Resposta do LLM
            
        Returns:
            Resposta com blocos de código formatados
        """
        processed_response = response
        
        # Encontrar todos os blocos de código
        for match in re.finditer(self.code_tag_pattern, response, re.DOTALL):
            language = match.group(1)
            code = match.group(2)
            
            # Criar nome de arquivo baseado no conteúdo
            file_path = self._guess_file_path(code, language)
            
            # Criar tag XML
            xml_code = (
                f'<augment_code_snippet path="{file_path}" mode="EXCERPT">\n'
                f'```{language}\n{code}\n```\n'
                f'</augment_code_snippet>'
            )
            
            # Substituir o bloco de código original pela tag XML
            full_match = match.group(0)
            processed_response = processed_response.replace(full_match, xml_code)
        
        return processed_response
    
    def _guess_file_path(self, code: str, language: str) -> str:
        """
        Tenta adivinhar um nome de arquivo baseado no conteúdo do código.
        
        Args:
            code: Conteúdo do código
            language: Linguagem de programação
            
        Returns:
            Nome de arquivo sugerido
        """
        # Mapeamento de linguagens para extensões
        language_extensions = {
            "python": ".py",
            "py": ".py",
            "javascript": ".js",
            "js": ".js",
            "typescript": ".ts",
            "ts": ".ts",
            "java": ".java",
            "c": ".c",
            "cpp": ".cpp",
            "csharp": ".cs",
            "cs": ".cs",
            "html": ".html",
            "css": ".css",
            "json": ".json",
            "xml": ".xml",
            "yaml": ".yaml",
            "yml": ".yml",
            "markdown": ".md",
            "md": ".md",
            "sql": ".sql",
            "bash": ".sh",
            "sh": ".sh",
            "": ".txt"  # Fallback para linguagem não especificada
        }
        
        # Obter extensão para a linguagem
        extension = language_extensions.get(language.lower(), ".txt")
        
        # Tentar encontrar um nome de classe ou função no código
        name = "example"
        
        # Para Python, procurar por definições de classe ou função
        if extension == ".py":
            class_match = re.search(r'class\s+(\w+)', code)
            if class_match:
                name = class_match.group(1).lower()
            else:
                func_match = re.search(r'def\s+(\w+)', code)
                if func_match:
                    name = func_match.group(1).lower()
        
        # Para JavaScript/TypeScript, procurar por definições de classe, função ou variável
        elif extension in [".js", ".ts"]:
            class_match = re.search(r'class\s+(\w+)', code)
            if class_match:
                name = class_match.group(1).lower()
            else:
                func_match = re.search(r'function\s+(\w+)', code)
                if func_match:
                    name = func_match.group(1).lower()
                else:
                    var_match = re.search(r'(const|let|var)\s+(\w+)', code)
                    if var_match:
                        name = var_match.group(2).lower()
        
        # Para Java/C#, procurar por definições de classe
        elif extension in [".java", ".cs"]:
            class_match = re.search(r'class\s+(\w+)', code)
            if class_match:
                name = class_match.group(1).lower()
        
        return f"{name}{extension}"
    
    def _add_conclusion(self, response: str) -> str:
        """
        Adiciona uma conclusão à resposta se não estiver presente.
        
        Args:
            response: Resposta do LLM
            
        Returns:
            Resposta com conclusão
        """
        if "## Conclusão" not in response:
            response += "\n\n## Conclusão\n\n"
            response += "Implementei a solução conforme solicitado. O código foi estruturado seguindo as melhores práticas e está pronto para uso."
        
        return response
