"""
Interface para Ferramentas

Este módulo define a interface para ferramentas do Augment Agent.
"""

import logging
import inspect
import json
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Tuple, Callable

# Configurar logger
logger = logging.getLogger("augment.tools")

class Tool:
    """
    Representa uma ferramenta disponível para o Augment Agent.
    """
    
    def __init__(self, name: str, description: str, function: Callable, 
                 parameters: Optional[Dict[str, Any]] = None):
        """
        Inicializa uma ferramenta.
        
        Args:
            name: Nome da ferramenta
            description: Descrição da ferramenta
            function: Função que implementa a ferramenta
            parameters: Parâmetros da ferramenta (opcional)
        """
        self.name = name
        self.description = description
        self.function = function
        self.parameters = parameters or {}
        
        # Extrair parâmetros da função se não fornecidos
        if not self.parameters:
            self._extract_parameters()
            
    def _extract_parameters(self):
        """
        Extrai parâmetros da função.
        """
        sig = inspect.signature(self.function)
        
        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue
                
            param_info = {
                'type': 'string',  # Padrão
                'description': '',
                'required': param.default == inspect.Parameter.empty
            }
            
            # Tentar extrair tipo e descrição da docstring
            if self.function.__doc__:
                param_doc = self._extract_param_doc(param_name)
                if param_doc:
                    param_info['description'] = param_doc
                    
            # Adicionar ao dicionário de parâmetros
            self.parameters[param_name] = param_info
            
    def _extract_param_doc(self, param_name: str) -> Optional[str]:
        """
        Extrai documentação de um parâmetro da docstring.
        
        Args:
            param_name: Nome do parâmetro
            
        Returns:
            Documentação do parâmetro ou None se não encontrada
        """
        if not self.function.__doc__:
            return None
            
        lines = self.function.__doc__.split('\n')
        for i, line in enumerate(lines):
            if f"{param_name}:" in line or f"{param_name} :" in line:
                # Extrair descrição
                parts = line.split(':', 1)
                if len(parts) > 1:
                    return parts[1].strip()
                    
        return None
        
    def execute(self, **kwargs) -> Any:
        """
        Executa a ferramenta.
        
        Args:
            **kwargs: Parâmetros para a ferramenta
            
        Returns:
            Resultado da execução
        """
        try:
            return self.function(**kwargs)
        except Exception as e:
            logger.error(f"Erro ao executar ferramenta {self.name}: {e}")
            raise
            
    def to_dict(self) -> Dict[str, Any]:
        """
        Converte a ferramenta para um dicionário.
        
        Returns:
            Dicionário com os dados da ferramenta
        """
        return {
            'name': self.name,
            'description': self.description,
            'parameters': self.parameters
        }
        
    def to_json(self) -> str:
        """
        Converte a ferramenta para JSON.
        
        Returns:
            JSON com os dados da ferramenta
        """
        return json.dumps(self.to_dict())

class ToolRegistry:
    """
    Registro de ferramentas disponíveis para o Augment Agent.
    """
    
    def __init__(self):
        """
        Inicializa o registro de ferramentas.
        """
        self.tools: Dict[str, Tool] = {}
        
    def register(self, tool: Tool):
        """
        Registra uma ferramenta.
        
        Args:
            tool: Ferramenta a ser registrada
        """
        self.tools[tool.name] = tool
        logger.info(f"Ferramenta registrada: {tool.name}")
        
    def register_function(self, name: str, description: str, 
                         parameters: Optional[Dict[str, Any]] = None):
        """
        Decorador para registrar uma função como ferramenta.
        
        Args:
            name: Nome da ferramenta
            description: Descrição da ferramenta
            parameters: Parâmetros da ferramenta (opcional)
            
        Returns:
            Decorador
        """
        def decorator(func):
            tool = Tool(name, description, func, parameters)
            self.register(tool)
            return func
        return decorator
        
    def get(self, name: str) -> Optional[Tool]:
        """
        Obtém uma ferramenta pelo nome.
        
        Args:
            name: Nome da ferramenta
            
        Returns:
            Ferramenta ou None se não encontrada
        """
        return self.tools.get(name)
        
    def list(self) -> List[str]:
        """
        Lista os nomes das ferramentas registradas.
        
        Returns:
            Lista de nomes de ferramentas
        """
        return list(self.tools.keys())
        
    def get_all(self) -> Dict[str, Tool]:
        """
        Obtém todas as ferramentas registradas.
        
        Returns:
            Dicionário de ferramentas
        """
        return self.tools.copy()
        
    def to_json(self) -> str:
        """
        Converte o registro de ferramentas para JSON.
        
        Returns:
            JSON com os dados das ferramentas
        """
        tools_dict = {name: tool.to_dict() for name, tool in self.tools.items()}
        return json.dumps(tools_dict)
        
    def execute(self, name: str, **kwargs) -> Any:
        """
        Executa uma ferramenta pelo nome.
        
        Args:
            name: Nome da ferramenta
            **kwargs: Parâmetros para a ferramenta
            
        Returns:
            Resultado da execução
        """
        tool = self.get(name)
        if not tool:
            raise ValueError(f"Ferramenta não encontrada: {name}")
            
        return tool.execute(**kwargs)
