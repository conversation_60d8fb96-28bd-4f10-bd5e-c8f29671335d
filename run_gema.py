"""
Executor Automático do Gema Agent

Este script permite executar o Gema Agent de forma automática com diferentes opções:
1. Modo interativo: conversa contínua com o agente
2. Modo de comando único: executa um único comando e exibe o resultado
3. Modo de arquivo de comandos: executa uma lista de comandos de um arquivo
4. Modo de script: executa o agente como parte de um script Python

Uso:
    python run_gema.py --interactive
    python run_gema.py --message "Crie um arquivo chamado exemplo.txt com o conteúdo 'Olá, mundo!'"
    python run_gema.py --file comandos.txt
    python run_gema.py --config config.json
"""

import os
import sys
import json
import argparse
import time
from pathlib import Path

# Adicionar diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar o Gema Agent
from gema_agent_fixed import GemaAgent

def load_config(config_path=None):
    """
    Carrega a configuração do Gema Agent.

    Args:
        config_path (str, optional): Caminho para o arquivo de configuração JSON.

    Returns:
        dict: Configuração carregada ou None se ocorrer um erro.
    """
    # Configuração padrão
    default_config = {
        'llm': {
            'type': 'local',
            'local': {
                'model_path': 'C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf',
                'n_ctx': 131072,
                'n_batch': 512,
                'n_gpu_layers': 0,
                'temperature': 0.7,
                'max_tokens': 1024
            }
        },
        'context_engine': {
            'embedding_dim': 768,
            'model_type': 'code',
            'cache_dir': './context_engine_cache',
            'max_results': 5
        },
        'agent': {
            'max_context_results': 5,
            'max_conversation_history': 10,
            'cache_dir': './agent_cache',
            'learning': {
                'enabled': True,
                'max_examples': 100,
                'similarity_threshold': 0.5,
                'feedback_threshold': 4,
                'cache_dir': './learning_cache'
            }
        }
    }

    # Se um caminho de configuração foi fornecido, tentar carregar
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)

            # Mesclar configurações
            def merge_dicts(d1, d2):
                for k, v in d2.items():
                    if k in d1 and isinstance(d1[k], dict) and isinstance(v, dict):
                        merge_dicts(d1[k], v)
                    else:
                        d1[k] = v

            # Criar uma cópia da configuração padrão
            config = default_config.copy()

            # Mesclar com a configuração do usuário
            merge_dicts(config, user_config)

            print(f"Configuração carregada de {config_path}")
            return config

        except Exception as e:
            print(f"Erro ao carregar configuração de {config_path}: {e}")
            print("Usando configuração padrão.")
            return default_config

    return default_config

def run_interactive_mode(agent):
    """
    Executa o Gema Agent em modo interativo.

    Args:
        agent (GemaAgent): Instância do Gema Agent.
    """
    print("\n" + "=" * 50)
    print("Gema Agent - Modo Interativo")
    print("Digite 'sair', 'exit' ou 'quit' para encerrar")
    print("Digite 'ajuda' ou 'help' para ver comandos disponíveis")
    print("=" * 50 + "\n")

    history = []

    while True:
        try:
            message = input("\n\033[1;34mVocê:\033[0m ")

            # Verificar comandos especiais
            if message.lower() in ['sair', 'exit', 'quit']:
                print("\nEncerrando Gema Agent...")
                break

            elif message.lower() in ['ajuda', 'help']:
                show_help()
                continue

            elif message.lower() in ['limpar', 'clear']:
                os.system('cls' if os.name == 'nt' else 'clear')
                continue

            elif message.lower() in ['histórico', 'history']:
                print("\nHistórico de comandos:")
                for i, cmd in enumerate(history, 1):
                    print(f"{i}. {cmd}")
                continue

            # Adicionar ao histórico
            history.append(message)

            # Processar mensagem
            start_time = time.time()
            response = agent.process_message(message)
            end_time = time.time()

            # Exibir resposta
            print(f"\n\033[1;32mGema ({end_time - start_time:.2f}s):\033[0m {response}")

            # Solicitar feedback (opcional)
            if len(history) % 5 == 0:  # A cada 5 comandos
                rating_input = input("\nAvalie a resposta (1-5, ou pressione Enter para pular): ")
                if rating_input.strip() and rating_input.isdigit():
                    rating = int(rating_input)
                    if 1 <= rating <= 5:
                        comment = input("Comentário (opcional): ")
                        agent.add_feedback(message, response, rating, comment)
                        print("Feedback registrado. Obrigado!")

        except KeyboardInterrupt:
            print("\n\nOperação interrompida pelo usuário.")
            print("Encerrando Gema Agent...")
            break

        except Exception as e:
            print(f"\n\033[1;31mErro:\033[0m {str(e)}")

def run_single_command(agent, message):
    """
    Executa um único comando no Gema Agent.

    Args:
        agent (GemaAgent): Instância do Gema Agent.
        message (str): Mensagem a ser processada.
    """
    try:
        print(f"\n\033[1;34mComando:\033[0m {message}")

        # Processar mensagem
        start_time = time.time()
        response = agent.process_message(message)
        end_time = time.time()

        # Exibir resposta
        print(f"\n\033[1;32mGema ({end_time - start_time:.2f}s):\033[0m {response}")

        return response

    except Exception as e:
        print(f"\n\033[1;31mErro:\033[0m {str(e)}")
        return None

def run_file_commands(agent, file_path):
    """
    Executa comandos de um arquivo no Gema Agent.

    Args:
        agent (GemaAgent): Instância do Gema Agent.
        file_path (str): Caminho para o arquivo de comandos.
    """
    try:
        # Verificar se o arquivo existe
        if not os.path.isfile(file_path):
            print(f"Erro: Arquivo não encontrado: {file_path}")
            return

        # Ler comandos do arquivo
        with open(file_path, 'r', encoding='utf-8') as f:
            commands = [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]

        print(f"Executando {len(commands)} comandos do arquivo {file_path}...")

        # Executar cada comando
        for i, command in enumerate(commands, 1):
            print(f"\n{'-' * 50}")
            print(f"Comando {i}/{len(commands)}")
            run_single_command(agent, command)

        print(f"\n{'-' * 50}")
        print(f"Concluído: {len(commands)} comandos executados.")

    except Exception as e:
        print(f"\n\033[1;31mErro:\033[0m {str(e)}")

def show_help():
    """Exibe ajuda sobre os comandos disponíveis."""
    print("\n" + "=" * 50)
    print("Comandos Especiais do Gema Agent:")
    print("=" * 50)
    print("  sair, exit, quit - Encerra o Gema Agent")
    print("  ajuda, help - Exibe esta ajuda")
    print("  limpar, clear - Limpa a tela")
    print("  histórico, history - Exibe o histórico de comandos")
    print("\nExemplos de Comandos para o Gema Agent:")
    print("  Crie um arquivo chamado exemplo.txt com o conteúdo 'Olá, mundo!'")
    print("  Leia o conteúdo do arquivo exemplo.txt")
    print("  Liste os arquivos no diretório atual")
    print("  Crie um script Python que calcule a sequência de Fibonacci")
    print("  Execute o comando 'dir' para listar os arquivos")
    print("=" * 50)

def main():
    """Função principal."""
    parser = argparse.ArgumentParser(description='Executor Automático do Gema Agent')
    parser.add_argument('--interactive', '-i', action='store_true', help='Modo interativo')
    parser.add_argument('--message', '-m', type=str, help='Mensagem para processar')
    parser.add_argument('--file', '-f', type=str, help='Arquivo de comandos para executar')
    parser.add_argument('--config', '-c', type=str, help='Arquivo de configuração JSON')

    args = parser.parse_args()

    # Carregar configuração
    config = load_config(args.config)

    # Inicializar agente
    print("Inicializando Gema Agent...")
    agent = GemaAgent(args.config)

    # Executar modo selecionado
    if args.interactive:
        run_interactive_mode(agent)

    elif args.message:
        run_single_command(agent, args.message)

    elif args.file:
        run_file_commands(agent, args.file)

    else:
        # Se nenhum modo foi especificado, mostrar ajuda e iniciar modo interativo
        parser.print_help()
        print("\nIniciando modo interativo por padrão...\n")
        run_interactive_mode(agent)

if __name__ == "__main__":
    main()
