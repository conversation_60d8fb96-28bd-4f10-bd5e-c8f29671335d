"""
Formatador de Código para o Gema Agent.

Este módulo implementa a formatação de código com tags XML no estilo do Augment Agent Auto.

Complexidade:
- Tempo: O(n) onde n é o tamanho do código
- Espaço: O(n) para armazenar o código formatado
"""

import re
import os
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CodeFormatter:
    """
    Formatador de código com tags XML no estilo do Augment Agent Auto.
    
    Esta classe implementa a formatação de código com tags XML no estilo do Augment Agent Auto,
    para melhorar a visualização de código nas respostas.
    
    Atributos:
        language_extensions (Dict[str, str]): Mapeamento de linguagens para extensões
        language_patterns (Dict[str, str]): Padrões para detectar linguagens
    """
    
    def __init__(self):
        """
        Inicializa o formatador de código.
        """
        # Mapeamento de linguagens para extensões
        self.language_extensions = {
            "python": ".py",
            "py": ".py",
            "javascript": ".js",
            "js": ".js",
            "typescript": ".ts",
            "ts": ".ts",
            "java": ".java",
            "c": ".c",
            "cpp": ".cpp",
            "csharp": ".cs",
            "cs": ".cs",
            "html": ".html",
            "css": ".css",
            "json": ".json",
            "xml": ".xml",
            "yaml": ".yaml",
            "yml": ".yml",
            "markdown": ".md",
            "md": ".md",
            "sql": ".sql",
            "bash": ".sh",
            "sh": ".sh",
            "": ".txt"  # Fallback para linguagem não especificada
        }
        
        # Padrões para detectar linguagens
        self.language_patterns = {
            "python": [
                r'import\s+[\w\.]+',
                r'from\s+[\w\.]+\s+import',
                r'def\s+\w+\s*\(',
                r'class\s+\w+\s*(\(|:)',
                r'if\s+__name__\s*==\s*[\'"]__main__[\'"]'
            ],
            "javascript": [
                r'const\s+\w+\s*=',
                r'let\s+\w+\s*=',
                r'var\s+\w+\s*=',
                r'function\s+\w+\s*\(',
                r'export\s+(default\s+)?(class|function)',
                r'import\s+.*\s+from\s+[\'"]'
            ],
            "typescript": [
                r'interface\s+\w+\s*\{',
                r'type\s+\w+\s*=',
                r'export\s+(default\s+)?(class|function|interface)',
                r'import\s+.*\s+from\s+[\'"]'
            ],
            "java": [
                r'public\s+(class|interface|enum)',
                r'private\s+(class|interface|enum)',
                r'protected\s+(class|interface|enum)',
                r'import\s+[\w\.]+;',
                r'package\s+[\w\.]+;'
            ],
            "csharp": [
                r'using\s+[\w\.]+;',
                r'namespace\s+[\w\.]+',
                r'public\s+(class|interface|enum|struct)',
                r'private\s+(class|interface|enum|struct)',
                r'protected\s+(class|interface|enum|struct)'
            ],
            "html": [
                r'<!DOCTYPE\s+html>',
                r'<html',
                r'<head',
                r'<body',
                r'<div',
                r'<span',
                r'<p>'
            ],
            "css": [
                r'[\w\.-]+\s*\{',
                r'@media',
                r'@import',
                r'@keyframes'
            ],
            "sql": [
                r'SELECT\s+.*\s+FROM',
                r'INSERT\s+INTO',
                r'UPDATE\s+.*\s+SET',
                r'DELETE\s+FROM',
                r'CREATE\s+TABLE'
            ],
            "bash": [
                r'#!/bin/(bash|sh)',
                r'export\s+\w+=',
                r'if\s+\[\s+.*\s+\];\s+then',
                r'for\s+\w+\s+in'
            ]
        }
        
        # Padrão para detectar blocos de código
        self.code_block_pattern = r'```(\w*)\n(.*?)```'
        
        # Padrão para detectar tags XML de código
        self.xml_code_pattern = r'<augment_code_snippet\s+path="([^"]+)"\s+mode="([^"]+)">\s*```+(\w*)\s*(.*?)\s*```+\s*</augment_code_snippet>'
    
    def format_code(self, code: str, language: str = "", file_path: str = "") -> str:
        """
        Formata código com tags XML no estilo do Augment Agent Auto.
        
        Args:
            code: Código a ser formatado
            language: Linguagem de programação (opcional)
            file_path: Caminho do arquivo (opcional)
            
        Returns:
            Código formatado com tags XML
            
        Complexidade:
            - Tempo: O(n) onde n é o tamanho do código
            - Espaço: O(n) para armazenar o código formatado
        """
        # Detectar linguagem se não for fornecida
        if not language:
            language = self._detect_language(code)
        
        # Gerar caminho de arquivo se não for fornecido
        if not file_path:
            file_path = self._generate_file_path(code, language)
        
        # Formatar código com tags XML
        formatted_code = (
            f'<augment_code_snippet path="{file_path}" mode="EXCERPT">\n'
            f'```{language}\n{code}\n```\n'
            f'</augment_code_snippet>'
        )
        
        return formatted_code
    
    def format_code_in_text(self, text: str) -> str:
        """
        Formata blocos de código em um texto com tags XML.
        
        Args:
            text: Texto contendo blocos de código
            
        Returns:
            Texto com blocos de código formatados
            
        Complexidade:
            - Tempo: O(n) onde n é o tamanho do texto
            - Espaço: O(n) para armazenar o texto formatado
        """
        # Verificar se o texto já contém tags XML de código
        if re.search(self.xml_code_pattern, text, re.DOTALL):
            return text
        
        # Formatar blocos de código
        formatted_text = text
        for match in re.finditer(self.code_block_pattern, text, re.DOTALL):
            language = match.group(1)
            code = match.group(2)
            
            # Formatar código
            formatted_code = self.format_code(code, language)
            
            # Substituir bloco de código original pelo formatado
            full_match = match.group(0)
            formatted_text = formatted_text.replace(full_match, formatted_code)
        
        return formatted_text
    
    def _detect_language(self, code: str) -> str:
        """
        Detecta a linguagem de programação de um código.
        
        Args:
            code: Código a ser analisado
            
        Returns:
            Linguagem detectada ou string vazia se não for possível detectar
            
        Complexidade:
            - Tempo: O(n*m) onde n é o tamanho do código e m é o número de padrões
            - Espaço: O(1)
        """
        # Verificar cada linguagem
        language_scores = {}
        for language, patterns in self.language_patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, code, re.IGNORECASE):
                    score += 1
            if score > 0:
                language_scores[language] = score
        
        # Retornar a linguagem com maior pontuação
        if language_scores:
            return max(language_scores.items(), key=lambda x: x[1])[0]
        
        return ""
    
    def _generate_file_path(self, code: str, language: str) -> str:
        """
        Gera um caminho de arquivo baseado no conteúdo do código.
        
        Args:
            code: Conteúdo do código
            language: Linguagem de programação
            
        Returns:
            Caminho de arquivo sugerido
            
        Complexidade:
            - Tempo: O(n) onde n é o tamanho do código
            - Espaço: O(1)
        """
        # Obter extensão para a linguagem
        extension = self.language_extensions.get(language.lower(), ".txt")
        
        # Tentar encontrar um nome de classe ou função no código
        name = "example"
        
        # Para Python, procurar por definições de classe ou função
        if extension == ".py":
            class_match = re.search(r'class\s+(\w+)', code)
            if class_match:
                name = class_match.group(1).lower()
            else:
                func_match = re.search(r'def\s+(\w+)', code)
                if func_match:
                    name = func_match.group(1).lower()
        
        # Para JavaScript/TypeScript, procurar por definições de classe, função ou variável
        elif extension in [".js", ".ts"]:
            class_match = re.search(r'class\s+(\w+)', code)
            if class_match:
                name = class_match.group(1).lower()
            else:
                func_match = re.search(r'function\s+(\w+)', code)
                if func_match:
                    name = func_match.group(1).lower()
                else:
                    var_match = re.search(r'(const|let|var)\s+(\w+)', code)
                    if var_match:
                        name = var_match.group(2).lower()
        
        # Para Java/C#, procurar por definições de classe
        elif extension in [".java", ".cs"]:
            class_match = re.search(r'class\s+(\w+)', code)
            if class_match:
                name = class_match.group(1).lower()
        
        return f"{name}{extension}"
    
    def extract_code_from_xml(self, xml_code: str) -> Tuple[str, str, str]:
        """
        Extrai código, linguagem e caminho de arquivo de uma tag XML.
        
        Args:
            xml_code: Tag XML de código
            
        Returns:
            Tupla (código, linguagem, caminho de arquivo)
            
        Complexidade:
            - Tempo: O(n) onde n é o tamanho da tag XML
            - Espaço: O(n) para armazenar o código extraído
        """
        match = re.search(self.xml_code_pattern, xml_code, re.DOTALL)
        if match:
            file_path = match.group(1)
            mode = match.group(2)
            language = match.group(3)
            code = match.group(4)
            return code, language, file_path
        
        return "", "", ""
