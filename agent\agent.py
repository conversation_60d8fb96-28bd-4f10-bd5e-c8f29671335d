"""
Agente Augment

Este módulo implementa o agente principal do Augment Agent Local.
"""

import os
import re
import json
import logging
import time
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
from pathlib import Path

from .conversation import Conversation, Message
from .learning import LearningSystem
from context_engine import ContextEngine
from llm_integration import LLMInterface
from tools import ToolRegistry
from utils.cache import MultiLevelCache

# Configurar logger
logger = logging.getLogger("augment.agent")

class AugmentAgent:
    """
    Agente principal do Augment Agent Local.

    Este agente coordena o motor de contexto, o modelo de linguagem e as ferramentas
    para fornecer assistência avançada de programação.
    """

    def __init__(self, llm: LLMInterface, context_engine: ContextEngine,
                 tool_registry: ToolRegistry, config: Optional[Dict[str, Any]] = None):
        """
        Inicializa o agente Augment.

        Args:
            llm: Interface para o modelo de linguagem
            context_engine: Motor de contexto
            tool_registry: Registro de ferramentas
            config: Configuração do agente (opcional)
        """
        self.llm = llm
        self.context_engine = context_engine
        self.tool_registry = tool_registry
        self.config = config or {}

        # Configurações
        self.max_context_results = self.config.get('max_context_results', 5)
        self.max_conversation_history = self.config.get('max_conversation_history', 10)
        self.cache_dir = Path(self.config.get('cache_dir', './agent_cache'))
        self.cache_dir.mkdir(exist_ok=True, parents=True)

        # Inicializar cache
        self.cache = MultiLevelCache(
            memory_cache_size=self.config.get('memory_cache_size', 1000),
            memory_ttl=self.config.get('memory_ttl', 3600),
            disk_cache_dir=str(self.cache_dir / 'cache'),
            disk_ttl=self.config.get('disk_ttl', 86400),
            disk_max_size_mb=self.config.get('disk_max_size_mb', 512)
        )

        # Inicializar sistema de aprendizado
        self.learning_system = LearningSystem(
            cache_dir=str(self.cache_dir / 'learning'),
            config=self.config.get('learning', {})
        )

        # Obter mensagem do sistema com aprimoramentos do sistema de aprendizado
        system_message = self.config.get('system_message', self._get_default_system_message())

        # Adicionar exemplos de aprendizado se disponíveis
        learning_enhancement = self.learning_system.get_system_prompt_enhancement()
        if learning_enhancement:
            system_message = f"{system_message}\n\n{learning_enhancement}"

        # Conversação atual
        self.conversation = Conversation(
            system_message=system_message
        )

        # Memória do agente (legado - usar learning_system.remember/recall para novos desenvolvimentos)
        self.memory: Dict[str, Any] = {}

        # Carregar memória se existir
        self._load_memory()

        logger.info("Agente Augment inicializado")

    def _get_default_system_message(self) -> str:
        """
        Obtém a mensagem do sistema padrão.

        Returns:
            Mensagem do sistema padrão
        """
        return """Você é o Augment Agent, um assistente de programação avançado baseado no Gema LLM.
Você tem acesso a um motor de contexto que pode buscar código relevante no codebase do usuário.
Você também tem acesso a ferramentas para manipulação de código, busca na web e execução de comandos.
Seu objetivo é ajudar o usuário a resolver problemas de programação, entender código e implementar novas funcionalidades.
Seja preciso, útil e forneça explicações claras. Quando apropriado, mostre exemplos de código.
"""

    def _load_memory(self) -> None:
        """
        Carrega a memória do agente do disco.
        """
        memory_path = self.cache_dir / 'memory.json'
        if memory_path.exists():
            try:
                with open(memory_path, 'r') as f:
                    self.memory = json.load(f)
                logger.info(f"Memória carregada: {len(self.memory)} itens")
            except Exception as e:
                logger.warning(f"Erro ao carregar memória: {e}")

    def _save_memory(self) -> None:
        """
        Salva a memória do agente no disco.
        """
        memory_path = self.cache_dir / 'memory.json'
        try:
            with open(memory_path, 'w') as f:
                json.dump(self.memory, f)
        except Exception as e:
            logger.warning(f"Erro ao salvar memória: {e}")

    def remember(self, key: str, value: Any) -> None:
        """
        Adiciona um item à memória do agente.

        Args:
            key: Chave do item
            value: Valor do item
        """
        self.memory[key] = value
        self._save_memory()

    def recall(self, key: str) -> Optional[Any]:
        """
        Recupera um item da memória do agente.

        Args:
            key: Chave do item

        Returns:
            Valor do item ou None se não encontrado
        """
        return self.memory.get(key)

    def process_message(self, message: str) -> str:
        """
        Processa uma mensagem do usuário e gera uma resposta.

        Args:
            message: Mensagem do usuário

        Returns:
            Resposta do agente
        """
        logger.info(f"Processando mensagem: {message[:50]}...")

        # Adicionar mensagem à conversação
        self.conversation.add_user_message(message)

        # Analisar consulta
        query_analysis = self.context_engine.analyze_query(message)

        # Recuperar contexto relevante
        context = self._retrieve_context(message, query_analysis)

        # Obter exemplos relevantes do sistema de aprendizado
        relevant_examples = self.learning_system.get_relevant_examples(message)
        if relevant_examples:
            # Adicionar exemplos ao contexto
            examples_context = {
                'content': "Exemplos de respostas similares:\n\n" + "\n\n".join([
                    f"Pergunta: {example['query']}\nResposta: {example['response']}"
                    for example in relevant_examples
                ]),
                'file_path': 'learning_examples.txt',
                'language': 'text',
                'relevance': 0.95  # Alta relevância
            }
            context.insert(0, examples_context)  # Inserir no início para maior prioridade

        # Preparar mensagens para o LLM
        messages = self._prepare_messages_for_llm()

        # Gerar resposta
        start_time = time.time()
        response = self.llm.chat_with_context(messages, context)
        end_time = time.time()

        logger.info(f"Resposta gerada em {end_time - start_time:.2f} segundos")

        # Processar resposta para executar ferramentas
        processed_response = self._process_response(response)

        # Adicionar resposta à conversação
        self.conversation.add_assistant_message(processed_response)

        # Armazenar a interação para aprendizado futuro
        # Não adicionamos feedback de rating aqui, isso seria feito pelo usuário posteriormente
        self.learning_system.remember(f"interaction_{int(time.time())}", {
            'query': message,
            'response': processed_response,
            'context_count': len(context),
            'response_time': end_time - start_time
        })

        return processed_response

    def _retrieve_context(self, query: str, query_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Recupera contexto relevante para a consulta.

        Args:
            query: Consulta do usuário
            query_analysis: Análise da consulta

        Returns:
            Lista de snippets de contexto
        """
        # Verificar cache
        cache_key = f"context_{hash(query)}"
        cached_context = self.cache.get(cache_key)
        if cached_context is not None:
            logger.info(f"Contexto encontrado no cache para: {query[:50]}...")
            return cached_context

        # Preparar filtros com base na análise da consulta
        filter_criteria = {}

        if query_analysis['languages']:
            filter_criteria['language'] = query_analysis['languages']

        if query_analysis['files']:
            # Se há arquivos específicos mencionados, buscar apenas nesses arquivos
            results = []
            for file_path in query_analysis['files']:
                file_results = self.context_engine.search_by_file(file_path, self.max_context_results)
                results.extend(file_results)

            # Limitar ao número máximo de resultados
            context = results[:self.max_context_results]
        else:
            # Caso contrário, buscar com base na consulta
            context = self.context_engine.retrieve_context(
                query=query,
                max_results=self.max_context_results,
                filter_criteria=filter_criteria
            )

        # Armazenar no cache
        self.cache.set(cache_key, context)

        return context

    def _prepare_messages_for_llm(self) -> List[Dict[str, str]]:
        """
        Prepara as mensagens da conversação para o LLM.

        Returns:
            Lista de mensagens no formato para LLM
        """
        # Obter todas as mensagens
        all_messages = self.conversation.get_messages_for_llm()

        # Limitar ao número máximo de mensagens
        if len(all_messages) > self.max_conversation_history + 1:  # +1 para a mensagem do sistema
            # Manter a mensagem do sistema e as últimas N mensagens
            messages = [all_messages[0]] + all_messages[-(self.max_conversation_history):]
        else:
            messages = all_messages

        return messages

    def _process_response(self, response: str) -> str:
        """
        Processa a resposta do LLM para executar ferramentas.

        Args:
            response: Resposta do LLM

        Returns:
            Resposta processada
        """
        # Verificar se há chamadas de ferramentas na resposta
        tool_calls = self._extract_tool_calls(response)

        if not tool_calls:
            return response

        # Executar ferramentas e substituir resultados
        processed_response = response

        for tool_name, args in tool_calls:
            tool = self.tool_registry.get(tool_name)

            if not tool:
                logger.warning(f"Ferramenta não encontrada: {tool_name}")
                continue

            try:
                # Executar ferramenta
                result = tool.execute(**args)

                # Converter resultado para string
                if isinstance(result, dict) or isinstance(result, list):
                    result_str = json.dumps(result, indent=2)
                else:
                    result_str = str(result)

                # Substituir chamada da ferramenta pelo resultado
                # Tentar os formatos possíveis
                tool_call_str1 = f"{{{{tool.{tool_name}({self._format_args(args)})}}}}"
                tool_call_str2 = f"tool.{tool_name}({self._format_args(args)})"
                tool_call_str3 = f"{tool_name}({self._format_args(args)})"

                # Substituir primeiro o formato com chaves
                processed_response = processed_response.replace(
                    tool_call_str1,
                    f"{{{{tool.{tool_name} result:}}}}\n```\n{result_str}\n```"
                )

                # Depois substituir o formato sem chaves
                processed_response = processed_response.replace(
                    tool_call_str2,
                    f"{{{{tool.{tool_name} result:}}}}\n```\n{result_str}\n```"
                )

                # Por fim, substituir o formato sem prefixo
                processed_response = processed_response.replace(
                    tool_call_str3,
                    f"{{{{tool.{tool_name} result:}}}}\n```\n{result_str}\n```"
                )
            except Exception as e:
                logger.error(f"Erro ao executar ferramenta {tool_name}: {e}")

                # Substituir chamada da ferramenta pelo erro
                # Tentar os formatos possíveis
                tool_call_str1 = f"{{{{tool.{tool_name}({self._format_args(args)})}}}}"
                tool_call_str2 = f"tool.{tool_name}({self._format_args(args)})"
                tool_call_str3 = f"{tool_name}({self._format_args(args)})"

                # Substituir primeiro o formato com chaves
                processed_response = processed_response.replace(
                    tool_call_str1,
                    f"{{{{tool.{tool_name} error:}}}}\n```\n{str(e)}\n```"
                )

                # Depois substituir o formato sem chaves
                processed_response = processed_response.replace(
                    tool_call_str2,
                    f"{{{{tool.{tool_name} error:}}}}\n```\n{str(e)}\n```"
                )

                # Por fim, substituir o formato sem prefixo
                processed_response = processed_response.replace(
                    tool_call_str3,
                    f"{{{{tool.{tool_name} error:}}}}\n```\n{str(e)}\n```"
                )

        return processed_response

    def _extract_tool_calls(self, text: str) -> List[Tuple[str, Dict[str, Any]]]:
        """
        Extrai chamadas de ferramentas do texto.

        Args:
            text: Texto a ser analisado

        Returns:
            Lista de tuplas (nome_da_ferramenta, argumentos)
        """
        tool_calls = []

        # Padrão para chamadas de ferramentas: {{tool.nome_da_ferramenta(arg1=valor1, arg2=valor2)}}
        # Também suporta o formato alternativo: tool.nome_da_ferramenta(arg1=valor1, arg2=valor2)
        # Também suporta o formato: write_file(filename='arquivo.txt', content='conteúdo')
        # Também suporta o formato: nome_da_ferramenta(arg1=valor1, arg2=valor2)
        pattern = r'(?:\{\{)?(?:tool\.)?(\w+)\((.*?)\)(?:\}\})?'

        for match in re.finditer(pattern, text):
            tool_name = match.group(1)
            args_str = match.group(2)

            # Extrair argumentos
            args = {}

            # Padrão para argumentos: nome=valor
            args_pattern = r'(\w+)=([^,]+)'

            for args_match in re.finditer(args_pattern, args_str):
                arg_name = args_match.group(1)
                arg_value_str = args_match.group(2).strip()

                # Converter valor para o tipo apropriado
                try:
                    # Tentar converter para JSON
                    arg_value = json.loads(arg_value_str)
                except json.JSONDecodeError:
                    # Se falhar, manter como string
                    arg_value = arg_value_str

                args[arg_name] = arg_value

            tool_calls.append((tool_name, args))

        return tool_calls

    def _format_args(self, args: Dict[str, Any]) -> str:
        """
        Formata argumentos para exibição.

        Args:
            args: Argumentos

        Returns:
            String formatada
        """
        return ", ".join(f"{k}={repr(v)}" for k, v in args.items())

    def reset_conversation(self) -> None:
        """
        Reinicia a conversação.
        """
        self.conversation = Conversation(
            system_message=self.config.get('system_message', self._get_default_system_message())
        )
        logger.info("Conversação reiniciada")

    def get_conversation(self) -> Conversation:
        """
        Obtém a conversação atual.

        Returns:
            Conversação atual
        """
        return self.conversation

    def set_conversation(self, conversation: Conversation) -> None:
        """
        Define a conversação atual.

        Args:
            conversation: Nova conversação
        """
        self.conversation = conversation
        logger.info(f"Conversação definida: {conversation.id}")

    def get_agent_info(self) -> Dict[str, Any]:
        """
        Obtém informações sobre o agente.

        Returns:
            Informações sobre o agente
        """
        return {
            'llm': self.llm.get_model_info(),
            'context_engine': self.context_engine.get_statistics(),
            'tools': self.tool_registry.list(),
            'memory_size': len(self.memory),
            'conversation_id': self.conversation.id,
            'conversation_messages': len(self.conversation.messages),
            'learning_system': self.learning_system.get_stats()
        }

    def add_feedback(self, query: str, response: str, rating: int, comment: Optional[str] = None) -> bool:
        """
        Adiciona feedback do usuário para aprendizado.

        Args:
            query: Consulta do usuário
            response: Resposta do agente
            rating: Avaliação (1-5)
            comment: Comentário opcional

        Returns:
            True se adicionou com sucesso, False caso contrário
        """
        return self.learning_system.add_feedback(query, response, rating, comment)
