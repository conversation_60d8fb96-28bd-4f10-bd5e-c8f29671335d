"""
Ferramentas de Arquivo

Este módulo implementa ferramentas para manipulação de arquivos.
"""

import os
import shutil
import logging
import glob
import json
import yaml
import csv
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple

from tools.tool_interface import Tool, ToolRegistry

# Configurar logger
logger = logging.getLogger("augment.tools.file")

class FileTools:
    """
    Ferramentas para manipulação de arquivos.
    """
    
    def __init__(self, registry: ToolRegistry):
        """
        Inicializa as ferramentas de arquivo.
        
        Args:
            registry: Registro de ferramentas
        """
        self.registry = registry
        
        # Registrar ferramentas
        self._register_tools()
        
    def _register_tools(self):
        """
        Registra as ferramentas de arquivo.
        """
        # Ferramenta para listar arquivos
        self.registry.register(Tool(
            name="list_files",
            description="Lista arquivos em um diretório",
            function=self.list_files,
            parameters={
                "path": {
                    "type": "string",
                    "description": "Caminho do diretório",
                    "required": True
                },
                "pattern": {
                    "type": "string",
                    "description": "Padrão para filtrar arquivos (glob)",
                    "required": False
                },
                "recursive": {
                    "type": "boolean",
                    "description": "Se deve listar arquivos recursivamente",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para ler arquivo
        self.registry.register(Tool(
            name="read_file",
            description="Lê o conteúdo de um arquivo",
            function=self.read_file,
            parameters={
                "path": {
                    "type": "string",
                    "description": "Caminho do arquivo",
                    "required": True
                },
                "encoding": {
                    "type": "string",
                    "description": "Codificação do arquivo",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para escrever arquivo
        self.registry.register(Tool(
            name="write_file",
            description="Escreve conteúdo em um arquivo",
            function=self.write_file,
            parameters={
                "path": {
                    "type": "string",
                    "description": "Caminho do arquivo",
                    "required": True
                },
                "content": {
                    "type": "string",
                    "description": "Conteúdo a ser escrito",
                    "required": True
                },
                "encoding": {
                    "type": "string",
                    "description": "Codificação do arquivo",
                    "required": False
                },
                "append": {
                    "type": "boolean",
                    "description": "Se deve anexar ao invés de sobrescrever",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para copiar arquivo
        self.registry.register(Tool(
            name="copy_file",
            description="Copia um arquivo",
            function=self.copy_file,
            parameters={
                "source": {
                    "type": "string",
                    "description": "Caminho do arquivo de origem",
                    "required": True
                },
                "destination": {
                    "type": "string",
                    "description": "Caminho do arquivo de destino",
                    "required": True
                },
                "overwrite": {
                    "type": "boolean",
                    "description": "Se deve sobrescrever o arquivo de destino se existir",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para mover arquivo
        self.registry.register(Tool(
            name="move_file",
            description="Move um arquivo",
            function=self.move_file,
            parameters={
                "source": {
                    "type": "string",
                    "description": "Caminho do arquivo de origem",
                    "required": True
                },
                "destination": {
                    "type": "string",
                    "description": "Caminho do arquivo de destino",
                    "required": True
                },
                "overwrite": {
                    "type": "boolean",
                    "description": "Se deve sobrescrever o arquivo de destino se existir",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para remover arquivo
        self.registry.register(Tool(
            name="remove_file",
            description="Remove um arquivo",
            function=self.remove_file,
            parameters={
                "path": {
                    "type": "string",
                    "description": "Caminho do arquivo",
                    "required": True
                }
            }
        ))
        
        # Ferramenta para criar diretório
        self.registry.register(Tool(
            name="create_directory",
            description="Cria um diretório",
            function=self.create_directory,
            parameters={
                "path": {
                    "type": "string",
                    "description": "Caminho do diretório",
                    "required": True
                },
                "parents": {
                    "type": "boolean",
                    "description": "Se deve criar diretórios pais",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para remover diretório
        self.registry.register(Tool(
            name="remove_directory",
            description="Remove um diretório",
            function=self.remove_directory,
            parameters={
                "path": {
                    "type": "string",
                    "description": "Caminho do diretório",
                    "required": True
                },
                "recursive": {
                    "type": "boolean",
                    "description": "Se deve remover recursivamente",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para obter informações de arquivo
        self.registry.register(Tool(
            name="get_file_info",
            description="Obtém informações sobre um arquivo",
            function=self.get_file_info,
            parameters={
                "path": {
                    "type": "string",
                    "description": "Caminho do arquivo",
                    "required": True
                }
            }
        ))
        
        # Ferramenta para ler arquivo JSON
        self.registry.register(Tool(
            name="read_json",
            description="Lê um arquivo JSON",
            function=self.read_json,
            parameters={
                "path": {
                    "type": "string",
                    "description": "Caminho do arquivo JSON",
                    "required": True
                },
                "encoding": {
                    "type": "string",
                    "description": "Codificação do arquivo",
                    "required": False
                }
            }
        ))
        
        # Ferramenta para escrever arquivo JSON
        self.registry.register(Tool(
            name="write_json",
            description="Escreve um objeto em um arquivo JSON",
            function=self.write_json,
            parameters={
                "path": {
                    "type": "string",
                    "description": "Caminho do arquivo JSON",
                    "required": True
                },
                "data": {
                    "type": "object",
                    "description": "Dados a serem escritos",
                    "required": True
                },
                "indent": {
                    "type": "integer",
                    "description": "Indentação",
                    "required": False
                },
                "encoding": {
                    "type": "string",
                    "description": "Codificação do arquivo",
                    "required": False
                }
            }
        ))
        
    def list_files(self, path: str, pattern: Optional[str] = None, recursive: bool = False) -> List[str]:
        """
        Lista arquivos em um diretório.
        
        Args:
            path: Caminho do diretório
            pattern: Padrão para filtrar arquivos (glob)
            recursive: Se deve listar arquivos recursivamente
            
        Returns:
            Lista de caminhos de arquivos
        """
        logger.info(f"Listando arquivos em {path}")
        
        try:
            # Normalizar caminho
            path = os.path.normpath(path)
            
            # Verificar se o diretório existe
            if not os.path.isdir(path):
                return []
                
            # Construir padrão
            if pattern:
                if recursive:
                    search_pattern = os.path.join(path, "**", pattern)
                else:
                    search_pattern = os.path.join(path, pattern)
            else:
                if recursive:
                    search_pattern = os.path.join(path, "**", "*")
                else:
                    search_pattern = os.path.join(path, "*")
                    
            # Listar arquivos
            files = glob.glob(search_pattern, recursive=recursive)
            
            # Filtrar diretórios
            files = [f for f in files if os.path.isfile(f)]
            
            return files
        except Exception as e:
            logger.error(f"Erro ao listar arquivos: {e}")
            return []
            
    def read_file(self, path: str, encoding: str = "utf-8") -> str:
        """
        Lê o conteúdo de um arquivo.
        
        Args:
            path: Caminho do arquivo
            encoding: Codificação do arquivo
            
        Returns:
            Conteúdo do arquivo
        """
        logger.info(f"Lendo arquivo: {path}")
        
        try:
            # Normalizar caminho
            path = os.path.normpath(path)
            
            # Verificar se o arquivo existe
            if not os.path.isfile(path):
                return f"Arquivo não encontrado: {path}"
                
            # Ler arquivo
            with open(path, "r", encoding=encoding) as f:
                content = f.read()
                
            return content
        except Exception as e:
            logger.error(f"Erro ao ler arquivo: {e}")
            return f"Erro ao ler arquivo: {e}"
            
    def write_file(self, path: str, content: str, encoding: str = "utf-8", append: bool = False) -> str:
        """
        Escreve conteúdo em um arquivo.
        
        Args:
            path: Caminho do arquivo
            content: Conteúdo a ser escrito
            encoding: Codificação do arquivo
            append: Se deve anexar ao invés de sobrescrever
            
        Returns:
            Mensagem de sucesso ou erro
        """
        logger.info(f"Escrevendo arquivo: {path}")
        
        try:
            # Normalizar caminho
            path = os.path.normpath(path)
            
            # Criar diretório pai se não existir
            os.makedirs(os.path.dirname(os.path.abspath(path)), exist_ok=True)
            
            # Escrever arquivo
            mode = "a" if append else "w"
            with open(path, mode, encoding=encoding) as f:
                f.write(content)
                
            return f"Arquivo escrito com sucesso: {path}"
        except Exception as e:
            logger.error(f"Erro ao escrever arquivo: {e}")
            return f"Erro ao escrever arquivo: {e}"
            
    def copy_file(self, source: str, destination: str, overwrite: bool = False) -> str:
        """
        Copia um arquivo.
        
        Args:
            source: Caminho do arquivo de origem
            destination: Caminho do arquivo de destino
            overwrite: Se deve sobrescrever o arquivo de destino se existir
            
        Returns:
            Mensagem de sucesso ou erro
        """
        logger.info(f"Copiando arquivo: {source} -> {destination}")
        
        try:
            # Normalizar caminhos
            source = os.path.normpath(source)
            destination = os.path.normpath(destination)
            
            # Verificar se o arquivo de origem existe
            if not os.path.isfile(source):
                return f"Arquivo de origem não encontrado: {source}"
                
            # Verificar se o arquivo de destino existe
            if os.path.exists(destination) and not overwrite:
                return f"Arquivo de destino já existe: {destination}"
                
            # Criar diretório pai se não existir
            os.makedirs(os.path.dirname(os.path.abspath(destination)), exist_ok=True)
            
            # Copiar arquivo
            shutil.copy2(source, destination)
            
            return f"Arquivo copiado com sucesso: {source} -> {destination}"
        except Exception as e:
            logger.error(f"Erro ao copiar arquivo: {e}")
            return f"Erro ao copiar arquivo: {e}"
            
    def move_file(self, source: str, destination: str, overwrite: bool = False) -> str:
        """
        Move um arquivo.
        
        Args:
            source: Caminho do arquivo de origem
            destination: Caminho do arquivo de destino
            overwrite: Se deve sobrescrever o arquivo de destino se existir
            
        Returns:
            Mensagem de sucesso ou erro
        """
        logger.info(f"Movendo arquivo: {source} -> {destination}")
        
        try:
            # Normalizar caminhos
            source = os.path.normpath(source)
            destination = os.path.normpath(destination)
            
            # Verificar se o arquivo de origem existe
            if not os.path.isfile(source):
                return f"Arquivo de origem não encontrado: {source}"
                
            # Verificar se o arquivo de destino existe
            if os.path.exists(destination) and not overwrite:
                return f"Arquivo de destino já existe: {destination}"
                
            # Criar diretório pai se não existir
            os.makedirs(os.path.dirname(os.path.abspath(destination)), exist_ok=True)
            
            # Mover arquivo
            shutil.move(source, destination)
            
            return f"Arquivo movido com sucesso: {source} -> {destination}"
        except Exception as e:
            logger.error(f"Erro ao mover arquivo: {e}")
            return f"Erro ao mover arquivo: {e}"
            
    def remove_file(self, path: str) -> str:
        """
        Remove um arquivo.
        
        Args:
            path: Caminho do arquivo
            
        Returns:
            Mensagem de sucesso ou erro
        """
        logger.info(f"Removendo arquivo: {path}")
        
        try:
            # Normalizar caminho
            path = os.path.normpath(path)
            
            # Verificar se o arquivo existe
            if not os.path.isfile(path):
                return f"Arquivo não encontrado: {path}"
                
            # Remover arquivo
            os.remove(path)
            
            return f"Arquivo removido com sucesso: {path}"
        except Exception as e:
            logger.error(f"Erro ao remover arquivo: {e}")
            return f"Erro ao remover arquivo: {e}"
            
    def create_directory(self, path: str, parents: bool = True) -> str:
        """
        Cria um diretório.
        
        Args:
            path: Caminho do diretório
            parents: Se deve criar diretórios pais
            
        Returns:
            Mensagem de sucesso ou erro
        """
        logger.info(f"Criando diretório: {path}")
        
        try:
            # Normalizar caminho
            path = os.path.normpath(path)
            
            # Criar diretório
            os.makedirs(path, exist_ok=True) if parents else os.mkdir(path)
            
            return f"Diretório criado com sucesso: {path}"
        except Exception as e:
            logger.error(f"Erro ao criar diretório: {e}")
            return f"Erro ao criar diretório: {e}"
            
    def remove_directory(self, path: str, recursive: bool = False) -> str:
        """
        Remove um diretório.
        
        Args:
            path: Caminho do diretório
            recursive: Se deve remover recursivamente
            
        Returns:
            Mensagem de sucesso ou erro
        """
        logger.info(f"Removendo diretório: {path}")
        
        try:
            # Normalizar caminho
            path = os.path.normpath(path)
            
            # Verificar se o diretório existe
            if not os.path.isdir(path):
                return f"Diretório não encontrado: {path}"
                
            # Remover diretório
            if recursive:
                shutil.rmtree(path)
            else:
                os.rmdir(path)
                
            return f"Diretório removido com sucesso: {path}"
        except Exception as e:
            logger.error(f"Erro ao remover diretório: {e}")
            return f"Erro ao remover diretório: {e}"
            
    def get_file_info(self, path: str) -> Dict[str, Any]:
        """
        Obtém informações sobre um arquivo.
        
        Args:
            path: Caminho do arquivo
            
        Returns:
            Informações sobre o arquivo
        """
        logger.info(f"Obtendo informações do arquivo: {path}")
        
        try:
            # Normalizar caminho
            path = os.path.normpath(path)
            
            # Verificar se o arquivo existe
            if not os.path.exists(path):
                return {"error": f"Arquivo não encontrado: {path}"}
                
            # Obter informações
            stat = os.stat(path)
            
            return {
                "path": path,
                "size": stat.st_size,
                "created": stat.st_ctime,
                "modified": stat.st_mtime,
                "accessed": stat.st_atime,
                "is_file": os.path.isfile(path),
                "is_dir": os.path.isdir(path),
                "extension": os.path.splitext(path)[1] if os.path.isfile(path) else None
            }
        except Exception as e:
            logger.error(f"Erro ao obter informações do arquivo: {e}")
            return {"error": f"Erro ao obter informações do arquivo: {e}"}
            
    def read_json(self, path: str, encoding: str = "utf-8") -> Dict[str, Any]:
        """
        Lê um arquivo JSON.
        
        Args:
            path: Caminho do arquivo JSON
            encoding: Codificação do arquivo
            
        Returns:
            Dados do arquivo JSON
        """
        logger.info(f"Lendo arquivo JSON: {path}")
        
        try:
            # Normalizar caminho
            path = os.path.normpath(path)
            
            # Verificar se o arquivo existe
            if not os.path.isfile(path):
                return {"error": f"Arquivo não encontrado: {path}"}
                
            # Ler arquivo
            with open(path, "r", encoding=encoding) as f:
                data = json.load(f)
                
            return data
        except Exception as e:
            logger.error(f"Erro ao ler arquivo JSON: {e}")
            return {"error": f"Erro ao ler arquivo JSON: {e}"}
            
    def write_json(self, path: str, data: Dict[str, Any], indent: int = 2, encoding: str = "utf-8") -> str:
        """
        Escreve um objeto em um arquivo JSON.
        
        Args:
            path: Caminho do arquivo JSON
            data: Dados a serem escritos
            indent: Indentação
            encoding: Codificação do arquivo
            
        Returns:
            Mensagem de sucesso ou erro
        """
        logger.info(f"Escrevendo arquivo JSON: {path}")
        
        try:
            # Normalizar caminho
            path = os.path.normpath(path)
            
            # Criar diretório pai se não existir
            os.makedirs(os.path.dirname(os.path.abspath(path)), exist_ok=True)
            
            # Escrever arquivo
            with open(path, "w", encoding=encoding) as f:
                json.dump(data, f, indent=indent)
                
            return f"Arquivo JSON escrito com sucesso: {path}"
        except Exception as e:
            logger.error(f"Erro ao escrever arquivo JSON: {e}")
            return f"Erro ao escrever arquivo JSON: {e}"
