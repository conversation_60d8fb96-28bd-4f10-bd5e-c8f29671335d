# Ferramentas do Gema para Augment Agent Local

Este conjunto de scripts fornece ferramentas simples para o Gema interagir com o Augment Agent Local.

## Instalação

1. Certifique-se de que todas as dependências estão instaladas:
   ```
   pip install -r requirements.txt
   ```

2. Os scripts já estão prontos para uso.

## Ferramentas Disponíveis

### Ferramentas de Arquivo

- `write_file`: Escreve conteúdo em um arquivo
- `read_file`: Lê o conteúdo de um arquivo
- `list_files`: Lista arquivos em um diretório
- `copy_file`: Copia um arquivo
- `move_file`: Move um arquivo
- `remove_file`: Remove um arquivo
- `create_directory`: Cria um diretório
- `remove_directory`: Remove um diretório
- `get_file_info`: Obtém informações sobre um arquivo
- `read_json`: Lê um arquivo JSON
- `write_json`: Escreve um objeto em um arquivo JSON

### Ferramentas de Código

- `search_code`: Busca código relevante no codebase
- `analyze_code`: Analisa um snippet de código
- `format_code`: Formata um snippet de código
- `execute_code`: Executa um snippet de código
- `lint_code`: Verifica erros em um snippet de código

### Ferramentas de Processo

- `execute_command`: Executa um comando e espera pela conclusão
- `start_process`: Inicia um processo em segundo plano
- `get_process`: Obtém informações de um processo
- `kill_process`: Mata um processo
- `list_processes`: Lista todos os processos

### Ferramentas de Busca

- `web_search`: Busca informações na web
- `search_documentation`: Busca documentação de bibliotecas e frameworks
- `search_code`: Busca exemplos de código
- `search_stackoverflow`: Busca perguntas e respostas no Stack Overflow
- `search_github`: Busca repositórios no GitHub
- `fetch_webpage`: Extrai o conteúdo de uma página web

### Sistema de Aprendizado

- `remember`: Armazena um item na memória
- `recall`: Recupera um item da memória
- `add_feedback`: Adiciona feedback do usuário
- `get_relevant_examples`: Obtém exemplos relevantes para uma consulta
- `get_system_prompt_enhancement`: Gera um aprimoramento para o prompt do sistema
- `get_stats`: Obtém estatísticas do sistema de aprendizado

## Como Usar

### Usando o Arquivo Batch

O arquivo `gema.bat` facilita o uso das ferramentas:

```
gema write_file --args "{'path': 'exemplo.txt', 'content': 'Olá, mundo!'}"
```

Para listar todas as ferramentas disponíveis:

```
gema --list
```

### Usando os Scripts Python Diretamente

Cada categoria de ferramentas tem seu próprio script:

```
python tools_file.py write --path exemplo.txt --content "Olá, mundo!"
```

### Usando o Script Unificado

O script `gema_tools.py` fornece acesso a todas as ferramentas em um único lugar:

```
python gema_tools.py write_file --args "{'path': 'exemplo.txt', 'content': 'Olá, mundo!'}"
```

## Treinamento do Gema

O script `train_gema.py` treina o Gema para usar as ferramentas, armazenando exemplos no sistema de auto-aprendizado:

```
python train_gema.py
```

Ou usando o arquivo batch:

```
treinar_gema
```

Você pode treinar categorias específicas:

```
treinar_gema --category file
```

Categorias disponíveis: `file`, `code`, `process`, `search`, `learning`, `all`

## Exemplos

### Criar um Arquivo

```
gema write_file --args "{'path': 'exemplo.txt', 'content': 'Olá, mundo!'}"
```

### Ler um Arquivo

```
gema read_file --args "{'path': 'exemplo.txt'}"
```

### Buscar na Web

```
gema web_search --args "{'query': 'Python generators', 'num_results': 3}"
```

### Executar um Comando

```
gema execute_command --args "{'command': 'dir'}"
```

### Armazenar na Memória

```
gema remember --args "{'key': 'user_preference', 'value': 'Prefere exemplos de código com comentários'}"
```

### Recuperar da Memória

```
gema recall --args "{'key': 'user_preference'}"
```

## Integração com o Augment Agent Local

Estas ferramentas são projetadas para serem usadas pelo Gema através do Augment Agent Local. O agente pode chamar estas ferramentas usando a sintaxe:

```
{{tool.nome_da_ferramenta(arg1=valor1, arg2=valor2)}}
```

Por exemplo:

```
{{tool.write_file(path='exemplo.txt', content='Olá, mundo!')}}
```

O sistema de auto-aprendizado armazena exemplos de uso das ferramentas, permitindo que o Gema aprenda a usá-las corretamente.
