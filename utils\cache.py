"""
Sistema de Cache em Múltiplos Níveis

Este módulo implementa um sistema de cache em múltiplos níveis para o Augment Agent,
permitindo armazenar e recuperar dados de forma eficiente em diferentes camadas
de armazenamento, com políticas de expiração e invalidação sofisticadas.
"""

import os
import time
import json
import pickle
import hashlib
import logging
import threading
import sqlite3
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple

# Configurar logger
logger = logging.getLogger("augment.cache")

class CacheEntry:
    """
    Entrada de cache com metadados.
    """

    def __init__(self, key: str, value: Any, ttl: int = 3600, metadata: Optional[Dict[str, Any]] = None):
        """
        Inicializa uma entrada de cache.

        Args:
            key: Chave da entrada
            value: Valor da entrada
            ttl: Tempo de vida em segundos
            metadata: Metadados adicionais
        """
        self.key = key
        self.value = value
        self.created_at = time.time()
        self.last_accessed = time.time()
        self.access_count = 0
        self.ttl = ttl
        self.metadata = metadata or {}

    def is_expired(self) -> bool:
        """
        Verifica se a entrada expirou.

        Returns:
            True se expirou, False caso contrário
        """
        return time.time() - self.created_at > self.ttl

    def access(self) -> None:
        """
        Registra um acesso à entrada.
        """
        self.last_accessed = time.time()
        self.access_count += 1

    def to_dict(self) -> Dict[str, Any]:
        """
        Converte a entrada para um dicionário.

        Returns:
            Dicionário com os dados da entrada
        """
        return {
            'key': self.key,
            'created_at': self.created_at,
            'last_accessed': self.last_accessed,
            'access_count': self.access_count,
            'ttl': self.ttl,
            'metadata': self.metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any], value: Any) -> 'CacheEntry':
        """
        Cria uma entrada a partir de um dicionário.

        Args:
            data: Dicionário com os dados da entrada
            value: Valor da entrada

        Returns:
            Entrada de cache
        """
        entry = cls(data['key'], value, data['ttl'], data['metadata'])
        entry.created_at = data['created_at']
        entry.last_accessed = data['last_accessed']
        entry.access_count = data['access_count']
        return entry

class MemoryCache:
    """
    Cache em memória com políticas de expiração LRU e LFU.
    """

    def __init__(self, max_size: int = 1000, ttl: int = 3600, eviction_policy: str = 'lru'):
        """
        Inicializa o cache em memória.

        Args:
            max_size: Tamanho máximo do cache
            ttl: Tempo de vida padrão em segundos
            eviction_policy: Política de remoção ('lru', 'lfu', 'fifo')
        """
        self.max_size = max_size
        self.default_ttl = ttl
        self.eviction_policy = eviction_policy
        self.cache: Dict[str, CacheEntry] = {}
        self.lock = threading.RLock()
        self.stats = {"hits": 0, "misses": 0}

    def get(self, key: str) -> Optional[Any]:
        """
        Obtém um valor do cache.

        Args:
            key: Chave do valor

        Returns:
            Valor ou None se não encontrado ou expirado
        """
        with self.lock:
            if key not in self.cache:
                self.stats["misses"] += 1
                return None

            entry = self.cache[key]

            # Verificar expiração
            if entry.is_expired():
                del self.cache[key]
                self.stats["misses"] += 1
                return None

            # Registrar acesso
            entry.access()
            self.stats["hits"] += 1

            return entry.value

    def set(self, key: str, value: Any, ttl: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Define um valor no cache.

        Args:
            key: Chave do valor
            value: Valor a ser armazenado
            ttl: Tempo de vida em segundos (opcional)
            metadata: Metadados adicionais (opcional)
        """
        with self.lock:
            # Verificar se precisa remover entradas
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict()

            # Criar entrada
            entry = CacheEntry(key, value, ttl or self.default_ttl, metadata)

            # Armazenar
            self.cache[key] = entry

    def delete(self, key: str) -> bool:
        """
        Remove um valor do cache.

        Args:
            key: Chave do valor

        Returns:
            True se removido, False caso contrário
        """
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False

    def clear(self) -> None:
        """
        Limpa o cache.
        """
        with self.lock:
            self.cache.clear()
            self.stats = {"hits": 0, "misses": 0}

    def _evict(self) -> None:
        """
        Remove entradas de acordo com a política de remoção.
        """
        if not self.cache:
            return

        if self.eviction_policy == 'lru':
            # Least Recently Used
            key_to_remove = min(self.cache.keys(), key=lambda k: self.cache[k].last_accessed)
        elif self.eviction_policy == 'lfu':
            # Least Frequently Used
            key_to_remove = min(self.cache.keys(), key=lambda k: self.cache[k].access_count)
        else:
            # FIFO (First In First Out)
            key_to_remove = min(self.cache.keys(), key=lambda k: self.cache[k].created_at)

        del self.cache[key_to_remove]

    def get_stats(self) -> Dict[str, Any]:
        """
        Obtém estatísticas do cache.

        Returns:
            Estatísticas do cache
        """
        with self.lock:
            total = self.stats["hits"] + self.stats["misses"]
            hit_ratio = self.stats["hits"] / total if total > 0 else 0

            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'eviction_policy': self.eviction_policy,
                'hits': self.stats["hits"],
                'misses': self.stats["misses"],
                'hit_ratio': hit_ratio
            }

class DiskCache:
    """
    Cache em disco usando SQLite para metadados e sistema de arquivos para valores.
    """

    def __init__(self, cache_dir: Union[str, Path], ttl: int = 86400, max_size_mb: int = 1024):
        """
        Inicializa o cache em disco.

        Args:
            cache_dir: Diretório para armazenar o cache
            ttl: Tempo de vida padrão em segundos
            max_size_mb: Tamanho máximo do cache em MB
        """
        self.cache_dir = Path(cache_dir)
        self.data_dir = self.cache_dir / 'data'
        self.default_ttl = ttl
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.lock = threading.RLock()
        self.stats = {"hits": 0, "misses": 0}

        # Criar diretórios
        self.cache_dir.mkdir(exist_ok=True, parents=True)
        self.data_dir.mkdir(exist_ok=True, parents=True)

        # Inicializar banco de dados
        self.db_path = self.cache_dir / 'cache.db'
        self._init_db()

    def _init_db(self) -> None:
        """
        Inicializa o banco de dados.
        """
        with self.lock:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            # Criar tabela de cache
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS cache (
                key TEXT PRIMARY KEY,
                created_at REAL,
                last_accessed REAL,
                access_count INTEGER,
                ttl INTEGER,
                size INTEGER,
                metadata TEXT
            )
            ''')

            # Criar índices
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_last_accessed ON cache (last_accessed)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_created_at ON cache (created_at)')

            conn.commit()
            conn.close()

    def get(self, key: str) -> Optional[Any]:
        """
        Obtém um valor do cache.

        Args:
            key: Chave do valor

        Returns:
            Valor ou None se não encontrado ou expirado
        """
        with self.lock:
            # Verificar se a chave existe no banco de dados
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute(
                'SELECT created_at, last_accessed, access_count, ttl, metadata FROM cache WHERE key = ?',
                (key,)
            )

            row = cursor.fetchone()

            if not row:
                conn.close()
                self.stats["misses"] += 1
                return None

            created_at, last_accessed, access_count, ttl, metadata_json = row

            # Verificar expiração
            if time.time() - created_at > ttl:
                # Remover entrada expirada
                cursor.execute('DELETE FROM cache WHERE key = ?', (key,))
                conn.commit()
                conn.close()

                # Remover arquivo de dados
                data_file = self.data_dir / f"{key}.pickle"
                if data_file.exists():
                    os.remove(data_file)

                self.stats["misses"] += 1
                return None

            # Carregar valor do arquivo
            data_file = self.data_dir / f"{key}.pickle"
            if not data_file.exists():
                conn.close()
                self.stats["misses"] += 1
                return None

            try:
                with open(data_file, 'rb') as f:
                    value = pickle.load(f)
            except Exception as e:
                logger.error(f"Erro ao carregar valor do cache: {e}")
                conn.close()
                self.stats["misses"] += 1
                return None

            # Atualizar estatísticas de acesso
            cursor.execute(
                'UPDATE cache SET last_accessed = ?, access_count = ? WHERE key = ?',
                (time.time(), access_count + 1, key)
            )

            conn.commit()
            conn.close()

            self.stats["hits"] += 1
            return value

    def set(self, key: str, value: Any, ttl: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Define um valor no cache.

        Args:
            key: Chave do valor
            value: Valor a ser armazenado
            ttl: Tempo de vida em segundos (opcional)
            metadata: Metadados adicionais (opcional)
        """
        with self.lock:
            # Verificar espaço disponível
            if self._get_cache_size() >= self.max_size_bytes:
                self._evict()

            # Salvar valor em arquivo
            data_file = self.data_dir / f"{key}.pickle"
            try:
                with open(data_file, 'wb') as f:
                    pickle.dump(value, f)
            except Exception as e:
                logger.error(f"Erro ao salvar valor no cache: {e}")
                return

            # Obter tamanho do arquivo
            size = os.path.getsize(data_file)

            # Salvar metadados no banco de dados
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            metadata_json = json.dumps(metadata or {})
            current_time = time.time()

            cursor.execute(
                '''
                INSERT OR REPLACE INTO cache
                (key, created_at, last_accessed, access_count, ttl, size, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ''',
                (key, current_time, current_time, 0, ttl or self.default_ttl, size, metadata_json)
            )

            conn.commit()
            conn.close()

    def delete(self, key: str) -> bool:
        """
        Remove um valor do cache.

        Args:
            key: Chave do valor

        Returns:
            True se removido, False caso contrário
        """
        with self.lock:
            # Remover do banco de dados
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute('DELETE FROM cache WHERE key = ?', (key,))
            deleted = cursor.rowcount > 0

            conn.commit()
            conn.close()

            # Remover arquivo de dados
            if deleted:
                data_file = self.data_dir / f"{key}.pickle"
                if data_file.exists():
                    os.remove(data_file)

            return deleted

    def clear(self) -> None:
        """
        Limpa o cache.
        """
        with self.lock:
            # Limpar banco de dados
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute('DELETE FROM cache')

            conn.commit()
            conn.close()

            # Limpar arquivos de dados
            for file in self.data_dir.glob("*.pickle"):
                os.remove(file)

            self.stats = {"hits": 0, "misses": 0}

    def _get_cache_size(self) -> int:
        """
        Obtém o tamanho total do cache em bytes.

        Returns:
            Tamanho do cache em bytes
        """
        conn = sqlite3.connect(str(self.db_path))
        cursor = conn.cursor()

        cursor.execute('SELECT SUM(size) FROM cache')
        row = cursor.fetchone()

        conn.close()

        return row[0] or 0

    def _evict(self) -> None:
        """
        Remove entradas para liberar espaço.
        """
        conn = sqlite3.connect(str(self.db_path))
        cursor = conn.cursor()

        # Remover entradas expiradas primeiro
        current_time = time.time()
        cursor.execute(
            'SELECT key FROM cache WHERE created_at + ttl < ?',
            (current_time,)
        )

        expired_keys = [row[0] for row in cursor.fetchall()]

        for key in expired_keys:
            cursor.execute('DELETE FROM cache WHERE key = ?', (key,))
            data_file = self.data_dir / f"{key}.pickle"
            if data_file.exists():
                os.remove(data_file)

        # Se ainda precisar de espaço, remover entradas menos acessadas
        if self._get_cache_size() >= self.max_size_bytes * 0.9:
            cursor.execute(
                'SELECT key FROM cache ORDER BY access_count, last_accessed LIMIT 10'
            )

            lru_keys = [row[0] for row in cursor.fetchall()]

            for key in lru_keys:
                cursor.execute('DELETE FROM cache WHERE key = ?', (key,))
                data_file = self.data_dir / f"{key}.pickle"
                if data_file.exists():
                    os.remove(data_file)

        conn.commit()
        conn.close()

    def get_stats(self) -> Dict[str, Any]:
        """
        Obtém estatísticas do cache.

        Returns:
            Estatísticas do cache
        """
        with self.lock:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute('SELECT COUNT(*) FROM cache')
            count = cursor.fetchone()[0]

            cursor.execute('SELECT SUM(size) FROM cache')
            size = cursor.fetchone()[0] or 0

            conn.close()

            total = self.stats["hits"] + self.stats["misses"]
            hit_ratio = self.stats["hits"] / total if total > 0 else 0

            return {
                'entries': count,
                'size_bytes': size,
                'max_size_bytes': self.max_size_bytes,
                'hits': self.stats["hits"],
                'misses': self.stats["misses"],
                'hit_ratio': hit_ratio
            }

class MultiLevelCache:
    """
    Cache em múltiplos níveis que combina cache em memória e em disco.

    Este cache usa uma estratégia de write-through e read-through para
    garantir consistência entre os níveis de cache.
    """

    def __init__(self,
                 memory_cache_size: int = 1000,
                 memory_ttl: int = 3600,
                 disk_cache_dir: Union[str, Path] = "./cache",
                 disk_ttl: int = 86400,
                 disk_max_size_mb: int = 1024,
                 eviction_policy: str = 'lru'):
        """
        Inicializa o cache em múltiplos níveis.

        Args:
            memory_cache_size: Tamanho máximo do cache em memória
            memory_ttl: Tempo de vida do cache em memória em segundos
            disk_cache_dir: Diretório para o cache em disco
            disk_ttl: Tempo de vida do cache em disco em segundos
            disk_max_size_mb: Tamanho máximo do cache em disco em MB
            eviction_policy: Política de remoção ('lru', 'lfu', 'fifo')
        """
        self.memory_cache = MemoryCache(
            max_size=memory_cache_size,
            ttl=memory_ttl,
            eviction_policy=eviction_policy
        )

        self.disk_cache = DiskCache(
            cache_dir=disk_cache_dir,
            ttl=disk_ttl,
            max_size_mb=disk_max_size_mb
        )

        self.lock = threading.RLock()

    def get(self, key: str) -> Optional[Any]:
        """
        Obtém um valor do cache.

        Primeiro tenta obter do cache em memória, depois do cache em disco.
        Se encontrado no disco mas não na memória, atualiza o cache em memória.

        Args:
            key: Chave do valor

        Returns:
            Valor ou None se não encontrado ou expirado
        """
        with self.lock:
            # Tentar obter do cache em memória
            value = self.memory_cache.get(key)
            if value is not None:
                return value

            # Tentar obter do cache em disco
            value = self.disk_cache.get(key)
            if value is not None:
                # Atualizar cache em memória
                self.memory_cache.set(key, value)
                return value

            return None

    def set(self, key: str, value: Any, ttl: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Define um valor no cache.

        Armazena o valor tanto no cache em memória quanto no cache em disco.

        Args:
            key: Chave do valor
            value: Valor a ser armazenado
            ttl: Tempo de vida em segundos (opcional)
            metadata: Metadados adicionais (opcional)
        """
        with self.lock:
            # Armazenar no cache em memória
            self.memory_cache.set(key, value, ttl, metadata)

            # Armazenar no cache em disco
            self.disk_cache.set(key, value, ttl, metadata)

    def delete(self, key: str) -> bool:
        """
        Remove um valor do cache.

        Args:
            key: Chave do valor

        Returns:
            True se removido, False caso contrário
        """
        with self.lock:
            # Remover de ambos os caches
            memory_deleted = self.memory_cache.delete(key)
            disk_deleted = self.disk_cache.delete(key)

            return memory_deleted or disk_deleted

    def clear(self) -> None:
        """
        Limpa o cache.
        """
        with self.lock:
            self.memory_cache.clear()
            self.disk_cache.clear()

    def get_stats(self) -> Dict[str, Any]:
        """
        Obtém estatísticas do cache.

        Returns:
            Estatísticas do cache
        """
        with self.lock:
            memory_stats = self.memory_cache.get_stats()
            disk_stats = self.disk_cache.get_stats()

            # Calcular taxa de acertos combinada
            memory_hits = memory_stats.get('hits', 0)
            memory_misses = memory_stats.get('misses', 0)
            disk_hits = disk_stats.get('hits', 0)
            disk_misses = disk_stats.get('misses', 0)

            total_hits = memory_hits + disk_hits
            total_misses = memory_misses + disk_misses
            total_requests = total_hits + total_misses

            hit_ratio = total_hits / total_requests if total_requests > 0 else 0

            return {
                'memory': memory_stats,
                'disk': disk_stats,
                'total_hits': total_hits,
                'total_misses': total_misses,
                'hit_ratio': hit_ratio
            }
