"""
Executor de Ferramentas para o Gema Agent.

Este módulo implementa um sistema robusto para executar chamadas de ferramentas,
garantindo que erros sejam tratados adequadamente e que os resultados sejam
formatados de forma consistente.

Complexidade:
- Tempo: O(1) por chamada de ferramenta
- Espaço: O(n) onde n é o tamanho do resultado da ferramenta
"""

import os
import json
import logging
import traceback
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ToolExecutor:
    """
    Executor de chamadas de ferramentas.
    
    Esta classe implementa um sistema robusto para executar chamadas de ferramentas,
    garantindo que erros sejam tratados adequadamente e que os resultados sejam
    formatados de forma consistente.
    
    Atributos:
        tools (Dict[str, Callable]): Dicionário de ferramentas disponíveis
        results_cache (Dict[str, Any]): Cache de resultados de ferramentas
    """
    
    def __init__(self, tools_registry=None):
        """
        Inicializa o executor de ferramentas.
        
        Args:
            tools_registry: Registro de ferramentas (opcional)
        """
        self.tools = {}
        self.results_cache = {}
        
        # Registrar ferramentas se fornecido
        if tools_registry:
            self._register_tools_from_registry(tools_registry)
    
    def _register_tools_from_registry(self, tools_registry):
        """
        Registra ferramentas a partir de um registro.
        
        Args:
            tools_registry: Registro de ferramentas
        """
        try:
            # Tentar obter ferramentas do registro
            if hasattr(tools_registry, 'get_all_tools'):
                tools = tools_registry.get_all_tools()
                for tool_name, tool in tools.items():
                    if hasattr(tool, 'function'):
                        self.register_tool(tool_name, tool.function)
                    else:
                        logger.warning(f"Ferramenta {tool_name} não tem atributo 'function'")
            else:
                logger.warning("Registro de ferramentas não tem método 'get_all_tools'")
        except Exception as e:
            logger.error(f"Erro ao registrar ferramentas do registro: {e}")
    
    def register_tool(self, tool_name: str, tool_function: Callable):
        """
        Registra uma ferramenta.
        
        Args:
            tool_name: Nome da ferramenta
            tool_function: Função da ferramenta
        """
        self.tools[tool_name] = tool_function
        logger.info(f"Ferramenta registrada: {tool_name}")
    
    def execute_tool_call(self, tool_call: Dict[str, Any]) -> Dict[str, Any]:
        """
        Executa uma chamada de ferramenta.
        
        Args:
            tool_call: Chamada de ferramenta
            
        Returns:
            Resultado da execução
            
        Complexidade:
            - Tempo: O(1) para a execução da ferramenta
            - Espaço: O(n) onde n é o tamanho do resultado
        """
        tool_name = tool_call.get("tool_name")
        parameters = tool_call.get("parameters", {})
        thought = tool_call.get("thought", "")
        
        # Verificar se a ferramenta existe
        if tool_name not in self.tools:
            error_msg = f"Ferramenta desconhecida: {tool_name}"
            logger.error(error_msg)
            return {
                "tool_name": tool_name,
                "success": False,
                "error": error_msg,
                "result": None,
                "thought": thought
            }
        
        # Executar a ferramenta
        try:
            # Obter a função da ferramenta
            tool_function = self.tools[tool_name]
            
            # Executar a função com os parâmetros
            result = tool_function(**parameters)
            
            # Armazenar resultado no cache
            cache_key = f"{tool_name}:{json.dumps(parameters, sort_keys=True)}"
            self.results_cache[cache_key] = result
            
            # Retornar resultado
            return {
                "tool_name": tool_name,
                "success": True,
                "error": None,
                "result": result,
                "thought": thought
            }
        except Exception as e:
            # Capturar e registrar o erro
            error_msg = f"Erro ao executar ferramenta {tool_name}: {e}"
            stack_trace = traceback.format_exc()
            logger.error(f"{error_msg}\n{stack_trace}")
            
            # Retornar erro
            return {
                "tool_name": tool_name,
                "success": False,
                "error": str(e),
                "stack_trace": stack_trace,
                "result": None,
                "thought": thought
            }
    
    def execute_tool_calls(self, tool_calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Executa uma lista de chamadas de ferramentas.
        
        Args:
            tool_calls: Lista de chamadas de ferramentas
            
        Returns:
            Lista de resultados
            
        Complexidade:
            - Tempo: O(n) onde n é o número de chamadas
            - Espaço: O(n) para armazenar os resultados
        """
        results = []
        
        for tool_call in tool_calls:
            result = self.execute_tool_call(tool_call)
            results.append(result)
        
        return results
    
    def format_results_for_llm(self, results: List[Dict[str, Any]]) -> str:
        """
        Formata os resultados para inclusão no prompt do LLM.
        
        Args:
            results: Lista de resultados
            
        Returns:
            String formatada com os resultados
            
        Complexidade:
            - Tempo: O(n) onde n é o número de resultados
            - Espaço: O(n) para armazenar a string formatada
        """
        formatted_results = "Resultados das ferramentas:\n\n"
        
        for i, result in enumerate(results):
            tool_name = result.get("tool_name", "unknown")
            success = result.get("success", False)
            error = result.get("error")
            tool_result = result.get("result")
            thought = result.get("thought", "")
            
            # Adicionar pensamento se disponível
            if thought:
                formatted_results += f"Pensamento: {thought}\n"
            
            # Adicionar resultado ou erro
            if success:
                formatted_results += f"Ferramenta: {tool_name}\n"
                formatted_results += f"Resultado: {tool_result}\n\n"
            else:
                formatted_results += f"Ferramenta: {tool_name}\n"
                formatted_results += f"Erro: {error}\n\n"
        
        return formatted_results
    
    def get_cached_result(self, tool_name: str, parameters: Dict[str, Any]) -> Optional[Any]:
        """
        Obtém um resultado do cache.
        
        Args:
            tool_name: Nome da ferramenta
            parameters: Parâmetros da ferramenta
            
        Returns:
            Resultado do cache ou None se não encontrado
            
        Complexidade:
            - Tempo: O(1) para acesso ao cache
            - Espaço: O(1)
        """
        cache_key = f"{tool_name}:{json.dumps(parameters, sort_keys=True)}"
        return self.results_cache.get(cache_key)
    
    def clear_cache(self):
        """
        Limpa o cache de resultados.
        
        Complexidade:
            - Tempo: O(1)
            - Espaço: O(1)
        """
        self.results_cache.clear()
        logger.info("Cache de resultados limpo")
