"""
Ferramentas de Código

Este módulo implementa ferramentas para manipulação de código.
"""

import os
import re
import logging
import subprocess
from typing import Dict, List, Any, Optional, Union, Tuple

from tools.tool_interface import Tool, ToolRegistry

# Configurar logger
logger = logging.getLogger("augment.tools.code")

class CodeTools:
    """
    Ferramentas para manipulação de código.
    """

    def __init__(self, registry: ToolRegistry, context_engine=None):
        """
        Inicializa as ferramentas de código.

        Args:
            registry: Registro de ferramentas
            context_engine: Motor de contexto (opcional)
        """
        self.registry = registry
        self.context_engine = context_engine

        # Registrar ferramentas
        self._register_tools()

    def _register_tools(self):
        """
        Registra as ferramentas de código.
        """
        # Ferramenta para buscar código
        self.registry.register(Tool(
            name="search_code",
            description="Busca código relevante no codebase",
            function=self.search_code,
            parameters={
                "query": {
                    "type": "string",
                    "description": "Consulta para busca de código",
                    "required": True
                },
                "max_results": {
                    "type": "integer",
                    "description": "Número máximo de resultados",
                    "required": False
                },
                "language": {
                    "type": "string",
                    "description": "Linguagem de programação",
                    "required": False
                }
            }
        ))

        # Ferramenta para analisar código
        self.registry.register(Tool(
            name="analyze_code",
            description="Analisa um snippet de código",
            function=self.analyze_code,
            parameters={
                "code": {
                    "type": "string",
                    "description": "Código a ser analisado",
                    "required": True
                },
                "language": {
                    "type": "string",
                    "description": "Linguagem de programação",
                    "required": False
                }
            }
        ))

        # Ferramenta para formatar código
        self.registry.register(Tool(
            name="format_code",
            description="Formata um snippet de código",
            function=self.format_code,
            parameters={
                "code": {
                    "type": "string",
                    "description": "Código a ser formatado",
                    "required": True
                },
                "language": {
                    "type": "string",
                    "description": "Linguagem de programação",
                    "required": False
                }
            }
        ))

        # Ferramenta para executar código
        self.registry.register(Tool(
            name="execute_code",
            description="Executa um snippet de código",
            function=self.execute_code,
            parameters={
                "code": {
                    "type": "string",
                    "description": "Código a ser executado",
                    "required": True
                },
                "language": {
                    "type": "string",
                    "description": "Linguagem de programação",
                    "required": True
                },
                "timeout": {
                    "type": "integer",
                    "description": "Tempo limite em segundos",
                    "required": False
                }
            }
        ))

        # Ferramenta para verificar erros de código
        self.registry.register(Tool(
            name="lint_code",
            description="Verifica erros em um snippet de código",
            function=self.lint_code,
            parameters={
                "code": {
                    "type": "string",
                    "description": "Código a ser verificado",
                    "required": True
                },
                "language": {
                    "type": "string",
                    "description": "Linguagem de programação",
                    "required": True
                }
            }
        ))

    def search_code(self, query: str, max_results: Optional[int] = None,
                   language: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Busca código relevante no codebase.

        Args:
            query: Consulta para busca de código
            max_results: Número máximo de resultados (opcional)
            language: Linguagem de programação (opcional)

        Returns:
            Lista de snippets de código relevantes
        """
        logger.info(f"Buscando código: {query}")

        if not self.context_engine:
            logger.warning("Motor de contexto não disponível")
            return []

        # Filtrar por linguagem se fornecida
        filter_criteria = None
        if language:
            filter_criteria = {"language": language}

        # Buscar no motor de contexto
        results = self.context_engine.retrieve_context(
            query=query,
            max_results=max_results,
            filter_criteria=filter_criteria
        )

        return results

    def analyze_code(self, code: str, language: Optional[str] = None) -> Dict[str, Any]:
        """
        Analisa um snippet de código.

        Args:
            code: Código a ser analisado
            language: Linguagem de programação (opcional)

        Returns:
            Análise do código
        """
        logger.info(f"Analisando código: {code[:50]}...")

        # Detectar linguagem se não fornecida
        if not language:
            language = self._detect_language(code)

        # Análise básica
        analysis = {
            "language": language,
            "lines": code.count("\n") + 1,
            "characters": len(code)
        }

        # Análise específica por linguagem
        if language == "python":
            analysis.update(self._analyze_python(code))
        elif language in ["javascript", "typescript"]:
            analysis.update(self._analyze_js(code))

        return analysis

    def _analyze_python(self, code: str) -> Dict[str, Any]:
        """
        Analisa código Python.

        Args:
            code: Código Python

        Returns:
            Análise do código
        """
        analysis = {}

        # Extrair imports
        import_pattern = r'^(?:from\s+[\w.]+\s+)?import\s+[\w., \t]+'
        imports = re.findall(import_pattern, code, re.MULTILINE)
        analysis["imports"] = imports

        # Extrair classes
        class_pattern = r'class\s+(\w+)(?:\([\w, ]*\))?:'
        classes = re.findall(class_pattern, code)
        analysis["classes"] = classes

        # Extrair funções
        func_pattern = r'def\s+(\w+)\s*\('
        functions = re.findall(func_pattern, code)
        analysis["functions"] = functions

        return analysis

    def _analyze_js(self, code: str) -> Dict[str, Any]:
        """
        Analisa código JavaScript/TypeScript.

        Args:
            code: Código JavaScript/TypeScript

        Returns:
            Análise do código
        """
        analysis = {}

        # Extrair imports
        import_pattern = r'(?:import|require)\s*\(?\s*[\w{}, .*\'"\s]+'
        imports = re.findall(import_pattern, code, re.MULTILINE)
        analysis["imports"] = imports

        # Extrair classes
        class_pattern = r'class\s+(\w+)'
        classes = re.findall(class_pattern, code)
        analysis["classes"] = classes

        # Extrair funções
        func_pattern = r'(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s*)?\([^)]*\)\s*=>|(\w+)\s*:\s*(?:async\s*)?\([^)]*\)\s*=>)'
        functions = []
        for match in re.finditer(func_pattern, code):
            func_name = next(filter(None, match.groups()), None)
            if func_name:
                functions.append(func_name)

        analysis["functions"] = functions

        return analysis

    def format_code(self, code: str, language: Optional[str] = None) -> str:
        """
        Formata um snippet de código.

        Args:
            code: Código a ser formatado
            language: Linguagem de programação (opcional)

        Returns:
            Código formatado
        """
        logger.info(f"Formatando código: {code[:50]}...")

        # Detectar linguagem se não fornecida
        if not language:
            language = self._detect_language(code)

        # Formatação básica
        lines = code.split("\n")
        formatted_lines = []
        prev_empty = False

        for line in lines:
            is_empty = not line.strip()

            # Evitar linhas em branco consecutivas
            if is_empty and prev_empty:
                continue

            formatted_lines.append(line)
            prev_empty = is_empty

        # Remover espaços em branco no final das linhas
        formatted_lines = [line.rstrip() for line in formatted_lines]

        # Remover linhas em branco no início e no final
        while formatted_lines and not formatted_lines[0].strip():
            formatted_lines.pop(0)

        while formatted_lines and not formatted_lines[-1].strip():
            formatted_lines.pop()

        return "\n".join(formatted_lines)

    def execute_code(self, code: str, language: str, timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Executa um snippet de código.

        Args:
            code: Código a ser executado
            language: Linguagem de programação
            timeout: Tempo limite em segundos (opcional)

        Returns:
            Resultado da execução
        """
        logger.info(f"Executando código {language}: {code[:50]}...")

        # Tempo limite padrão
        if timeout is None:
            timeout = 10

        # Executar código de acordo com a linguagem
        if language == "python":
            return self._execute_python(code, timeout)
        elif language == "javascript":
            return self._execute_js(code, timeout)
        else:
            return {
                "success": False,
                "error": f"Linguagem não suportada: {language}"
            }

    def _execute_python(self, code: str, timeout: int) -> Dict[str, Any]:
        """
        Executa código Python.

        Args:
            code: Código Python
            timeout: Tempo limite em segundos

        Returns:
            Resultado da execução
        """
        # Criar arquivo temporário
        import tempfile

        with tempfile.NamedTemporaryFile(suffix=".py", delete=False) as f:
            f.write(code.encode("utf-8"))
            temp_file = f.name

        try:
            # Executar código
            result = subprocess.run(
                ["python", temp_file],
                capture_output=True,
                text=True,
                timeout=timeout
            )

            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": f"Tempo limite excedido ({timeout}s)"
            }
        finally:
            # Remover arquivo temporário
            os.unlink(temp_file)

    def _execute_js(self, code: str, timeout: int) -> Dict[str, Any]:
        """
        Executa código JavaScript.

        Args:
            code: Código JavaScript
            timeout: Tempo limite em segundos

        Returns:
            Resultado da execução
        """
        # Criar arquivo temporário
        import tempfile

        with tempfile.NamedTemporaryFile(suffix=".js", delete=False) as f:
            f.write(code.encode("utf-8"))
            temp_file = f.name

        try:
            # Executar código
            result = subprocess.run(
                ["node", temp_file],
                capture_output=True,
                text=True,
                timeout=timeout
            )

            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": f"Tempo limite excedido ({timeout}s)"
            }
        finally:
            # Remover arquivo temporário
            os.unlink(temp_file)

    def lint_code(self, code: str, language: str) -> Dict[str, Any]:
        """
        Verifica erros em um snippet de código.

        Args:
            code: Código a ser verificado
            language: Linguagem de programação

        Returns:
            Resultado da verificação
        """
        logger.info(f"Verificando erros em código {language}: {code[:50]}...")

        # Verificar erros de acordo com a linguagem
        if language == "python":
            return self._lint_python(code)
        elif language in ["javascript", "typescript"]:
            return self._lint_js(code, language)
        else:
            return {
                "success": False,
                "error": f"Linguagem não suportada: {language}"
            }

    def _lint_python(self, code: str) -> Dict[str, Any]:
        """
        Verifica erros em código Python.

        Args:
            code: Código Python

        Returns:
            Resultado da verificação
        """
        # Verificar erros de sintaxe
        try:
            compile(code, "<string>", "exec")
            return {
                "success": True,
                "errors": []
            }
        except SyntaxError as e:
            return {
                "success": False,
                "errors": [{
                    "line": e.lineno,
                    "column": e.offset,
                    "message": str(e)
                }]
            }

    def _lint_js(self, code: str, language: str) -> Dict[str, Any]:
        """
        Verifica erros em código JavaScript/TypeScript.

        Args:
            code: Código JavaScript/TypeScript
            language: Linguagem de programação

        Returns:
            Resultado da verificação
        """
        # Criar arquivo temporário
        import tempfile

        ext = ".ts" if language == "typescript" else ".js"

        with tempfile.NamedTemporaryFile(suffix=ext, delete=False) as f:
            f.write(code.encode("utf-8"))
            temp_file = f.name

        try:
            # Verificar erros de sintaxe
            result = subprocess.run(
                ["node", "--check", temp_file],
                capture_output=True,
                text=True
            )

            if result.returncode == 0:
                return {
                    "success": True,
                    "errors": []
                }
            else:
                # Extrair erros
                errors = []
                for line in result.stderr.split("\n"):
                    if line.strip():
                        errors.append({
                            "message": line.strip()
                        })

                return {
                    "success": False,
                    "errors": errors
                }
        finally:
            # Remover arquivo temporário
            os.unlink(temp_file)

    def _detect_language(self, code: str) -> str:
        """
        Detecta a linguagem de programação de um snippet de código.

        Args:
            code: Snippet de código

        Returns:
            Linguagem detectada ou "unknown" se não for possível detectar
        """
        # Implementação simplificada
        if "def " in code and "import " in code:
            return "python"
        elif "function " in code or "const " in code or "let " in code:
            return "javascript"
        elif "public class " in code or "private class " in code:
            return "java"
        elif "#include" in code:
            return "cpp"
        elif "using namespace" in code or "using System" in code:
            return "csharp"

        return "unknown"
