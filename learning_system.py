"""
Learning System - Sistema de aprendizado para o Gema Agent

Este módulo implementa um sistema de aprendizado para o Gema Agent,
permitindo que ele armazene e recupere exemplos de interações anteriores.

Complexidade:
- Tempo: O(n) para busca de exemplos relevantes, onde n é o número de exemplos
- Espaço: O(n) para armazenar os exemplos
"""

import os
import json
import logging
import time
import random
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LearningSystem:
    """
    Sistema de aprendizado para o Gema Agent.
    
    Esta classe implementa um sistema de aprendizado para o Gema Agent,
    permitindo que ele armazene e recupere exemplos de interações anteriores.
    
    Atributos:
        cache_dir (str): Diretório para armazenar o cache de exemplos
        examples (List[Dict[str, Any]]): Lista de exemplos
        config (Dict[str, Any]): Configuração do sistema de aprendizado
    """
    
    def __init__(self, cache_dir: str = "cache", config: Optional[Dict[str, Any]] = None):
        """
        Inicializa o sistema de aprendizado.
        
        Args:
            cache_dir (str, optional): Diretório para armazenar o cache de exemplos
            config (Dict[str, Any], optional): Configuração do sistema de aprendizado
        """
        self.cache_dir = cache_dir
        self.config = config or {}
        self.examples = []
        
        # Criar diretório de cache se não existir
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Carregar exemplos do cache
        self._load_examples()
    
    def _load_examples(self):
        """
        Carrega exemplos do cache.
        
        Complexidade:
            - Tempo: O(n) onde n é o número de exemplos
            - Espaço: O(n) para armazenar os exemplos
        """
        cache_file = os.path.join(self.cache_dir, "examples.json")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, "r", encoding="utf-8") as f:
                    self.examples = json.load(f)
                logger.info(f"Carregados {len(self.examples)} exemplos do cache")
            except Exception as e:
                logger.error(f"Erro ao carregar exemplos do cache: {e}")
                self.examples = []
    
    def _save_examples(self):
        """
        Salva exemplos no cache.
        
        Complexidade:
            - Tempo: O(n) onde n é o número de exemplos
            - Espaço: O(n) para armazenar os exemplos
        """
        cache_file = os.path.join(self.cache_dir, "examples.json")
        
        try:
            with open(cache_file, "w", encoding="utf-8") as f:
                json.dump(self.examples, f, ensure_ascii=False, indent=2)
            logger.info(f"Salvos {len(self.examples)} exemplos no cache")
        except Exception as e:
            logger.error(f"Erro ao salvar exemplos no cache: {e}")
    
    def add_example(self, query: str, response: str, score: int = 3):
        """
        Adiciona um exemplo ao sistema de aprendizado.
        
        Args:
            query (str): Consulta do usuário
            response (str): Resposta do sistema
            score (int, optional): Pontuação do exemplo (1-5)
            
        Complexidade:
            - Tempo: O(1)
            - Espaço: O(n) onde n é o tamanho do exemplo
        """
        # Verificar se o exemplo já existe
        for example in self.examples:
            if example["query"] == query and example["response"] == response:
                # Atualizar pontuação
                example["score"] = score
                example["updated_at"] = time.time()
                logger.info(f"Exemplo atualizado: {query[:50]}...")
                self._save_examples()
                return
        
        # Adicionar novo exemplo
        example = {
            "query": query,
            "response": response,
            "score": score,
            "created_at": time.time(),
            "updated_at": time.time(),
            "used_count": 0
        }
        
        self.examples.append(example)
        logger.info(f"Exemplo adicionado: {query[:50]}...")
        
        # Salvar exemplos no cache
        self._save_examples()
    
    def get_relevant_examples(self, query: str, max_examples: int = 3) -> List[Dict[str, Any]]:
        """
        Obtém exemplos relevantes para uma consulta.
        
        Args:
            query (str): Consulta do usuário
            max_examples (int, optional): Número máximo de exemplos a retornar
            
        Returns:
            List[Dict[str, Any]]: Lista de exemplos relevantes
            
        Complexidade:
            - Tempo: O(n) onde n é o número de exemplos
            - Espaço: O(m) onde m é o número de exemplos retornados
        """
        if not self.examples:
            return []
        
        # Calcular relevância de cada exemplo
        relevant_examples = []
        
        for example in self.examples:
            # Calcular similaridade entre a consulta e o exemplo
            similarity = self._calculate_similarity(query, example["query"])
            
            # Adicionar exemplo com sua relevância
            relevant_examples.append({
                "example": example,
                "relevance": similarity * example["score"]
            })
        
        # Ordenar exemplos por relevância
        relevant_examples.sort(key=lambda x: x["relevance"], reverse=True)
        
        # Selecionar os exemplos mais relevantes
        selected_examples = []
        
        for item in relevant_examples[:max_examples]:
            example = item["example"]
            
            # Incrementar contador de uso
            example["used_count"] += 1
            
            # Adicionar exemplo selecionado
            selected_examples.append({
                "query": example["query"],
                "response": example["response"],
                "score": example["score"]
            })
        
        # Salvar exemplos no cache (para atualizar contadores de uso)
        self._save_examples()
        
        return selected_examples
    
    def _calculate_similarity(self, query1: str, query2: str) -> float:
        """
        Calcula a similaridade entre duas consultas.
        
        Esta função implementa uma versão simplificada de similaridade baseada em tokens.
        Para uma implementação mais avançada, seria necessário usar embeddings.
        
        Args:
            query1 (str): Primeira consulta
            query2 (str): Segunda consulta
            
        Returns:
            float: Similaridade entre as consultas (0-1)
            
        Complexidade:
            - Tempo: O(n) onde n é o tamanho das consultas
            - Espaço: O(n) para armazenar os tokens
        """
        # Converter para minúsculas
        query1 = query1.lower()
        query2 = query2.lower()
        
        # Tokenizar (versão simplificada)
        tokens1 = set(query1.split())
        tokens2 = set(query2.split())
        
        # Calcular interseção e união
        intersection = tokens1.intersection(tokens2)
        union = tokens1.union(tokens2)
        
        # Calcular similaridade de Jaccard
        if not union:
            return 0.0
        
        return len(intersection) / len(union)
    
    def get_system_prompt_enhancement(self) -> str:
        """
        Obtém um aprimoramento para o prompt do sistema com base nos exemplos.
        
        Returns:
            str: Aprimoramento para o prompt do sistema
            
        Complexidade:
            - Tempo: O(1)
            - Espaço: O(1)
        """
        if not self.examples:
            return ""
        
        # Obter estatísticas dos exemplos
        num_examples = len(self.examples)
        avg_score = sum(example["score"] for example in self.examples) / num_examples
        
        # Gerar aprimoramento
        enhancement = f"""
Você tem acesso a {num_examples} exemplos de interações anteriores, com uma pontuação média de {avg_score:.1f}/5.
Use esses exemplos para melhorar suas respostas, seguindo o estilo e formato das respostas bem avaliadas.
"""
        
        return enhancement
    
    def clear_examples(self):
        """
        Limpa todos os exemplos.
        
        Complexidade:
            - Tempo: O(1)
            - Espaço: O(1)
        """
        self.examples = []
        self._save_examples()
        logger.info("Exemplos limpos")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Obtém estatísticas do sistema de aprendizado.
        
        Returns:
            Dict[str, Any]: Estatísticas do sistema
            
        Complexidade:
            - Tempo: O(n) onde n é o número de exemplos
            - Espaço: O(1)
        """
        if not self.examples:
            return {
                "num_examples": 0,
                "avg_score": 0.0,
                "max_score": 0.0,
                "min_score": 0.0,
                "avg_used_count": 0.0,
                "max_used_count": 0
            }
        
        num_examples = len(self.examples)
        scores = [example["score"] for example in self.examples]
        used_counts = [example.get("used_count", 0) for example in self.examples]
        
        return {
            "num_examples": num_examples,
            "avg_score": sum(scores) / num_examples,
            "max_score": max(scores),
            "min_score": min(scores),
            "avg_used_count": sum(used_counts) / num_examples,
            "max_used_count": max(used_counts)
        }
    
    def __len__(self) -> int:
        """
        Retorna o número de exemplos.
        
        Returns:
            int: Número de exemplos
        """
        return len(self.examples)
    
    def __str__(self) -> str:
        """
        Retorna uma representação em string do sistema de aprendizado.
        
        Returns:
            str: Representação em string
        """
        stats = self.get_statistics()
        return f"LearningSystem com {stats['num_examples']} exemplos, pontuação média: {stats['avg_score']:.1f}/5"
