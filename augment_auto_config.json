{"llm": {"type": "local", "local": {"model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf", "n_ctx": 8192, "n_batch": 512, "n_gpu_layers": 0, "temperature": 0.7, "max_tokens": 4096, "unlimited_context": true, "overlap_ratio": 0.1, "importance_threshold": 0.7}}, "context_engine": {"embedding_dim": 768, "model_type": "code", "cache_dir": "./context_engine_cache", "max_results": 5, "supported_extensions": [".py", ".js", ".ts", ".java", ".c", ".cpp", ".cs", ".go", ".rb", ".php", ".swift", ".kt", ".rs", ".scala", ".html", ".css", ".jsx", ".tsx", ".md", ".json", ".yaml", ".yml", ".xml", ".sql", ".sh", ".bat", ".ps1"], "max_file_size": 1048576, "chunk_size": 1000, "chunk_overlap": 200, "use_faiss": true}, "agent": {"max_context_results": 5, "max_conversation_history": 10, "cache_dir": "./agent_cache", "memory_cache_size": 1000, "memory_ttl": 3600, "disk_ttl": 86400, "disk_max_size_mb": 512, "learning": {"enabled": true, "max_examples": 100, "similarity_threshold": 0.5, "feedback_threshold": 4, "cache_dir": "./learning_cache"}}, "ui": {"type": "cli", "web": {"host": "127.0.0.1", "port": 5000, "open_browser": true, "advanced": true}}}