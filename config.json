{"llm": {"type": "local", "local": {"model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf", "n_ctx": 131072, "n_batch": 512, "n_gpu_layers": 43, "temperature": 0.7, "max_tokens": 4096, "top_p": 0.95, "frequency_penalty": 0.0, "presence_penalty": 0.0, "unlimited_context": true}}, "context_engine": {"embedding_dim": 768, "model_type": "local", "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\nomic-ai\\nomic-embed-text-v2-moe-GGUF\\nomic-embed-text.gguf", "cache_dir": "./context_engine_cache", "max_results": 10, "hierarchical_search": true}, "agent": {"max_context_results": 10, "max_conversation_history": 20, "cache_dir": "./agent_cache", "learning": {"enabled": true, "max_examples": 200, "similarity_threshold": 0.7, "feedback_threshold": 4, "cache_dir": "./learning_cache"}}}