"""
Testes para o Agente

Este módulo contém testes para o agente.
"""

import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import tempfile
import shutil
from pathlib import Path

# Adicionar diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent.agent import AugmentAgent
from agent.conversation import Conversation, Message

class TestAgent(unittest.TestCase):
    """
    Testes para o agente.
    """
    
    def setUp(self):
        """
        Configuração para os testes.
        """
        # Criar mocks
        self.llm = MagicMock()
        self.context_engine = MagicMock()
        self.tool_registry = MagicMock()
        
        # Configurar diretório temporário
        self.temp_dir = tempfile.mkdtemp()
        
        # Criar agente
        self.agent = AugmentAgent(
            llm=self.llm,
            context_engine=self.context_engine,
            tool_registry=self.tool_registry,
            config={
                'cache_dir': self.temp_dir
            }
        )
        
    def tearDown(self):
        """
        Limpeza após os testes.
        """
        shutil.rmtree(self.temp_dir)
        
    def test_process_message(self):
        """
        Testa o processamento de mensagens.
        """
        # Configurar mocks
        self.context_engine.analyze_query.return_value = {
            'query_type': 'code_search',
            'languages': ['python'],
            'files': []
        }
        self.context_engine.retrieve_context.return_value = [
            {
                'content': 'def hello_world():\n    print("Hello, world!")',
                'file_path': 'hello.py',
                'start_line': 1,
                'end_line': 2,
                'language': 'python'
            }
        ]
        self.llm.chat_with_context.return_value = "Aqui está um exemplo de função que imprime 'Hello, world!' em Python:\n```python\ndef hello_world():\n    print('Hello, world!')\n```"
        
        # Processar mensagem
        response = self.agent.process_message("Como imprimir 'Hello, world!' em Python?")
        
        # Verificar chamadas
        self.context_engine.analyze_query.assert_called_once()
        self.context_engine.retrieve_context.assert_called_once()
        self.llm.chat_with_context.assert_called_once()
        
        # Verificar resposta
        self.assertEqual(response, "Aqui está um exemplo de função que imprime 'Hello, world!' em Python:\n```python\ndef hello_world():\n    print('Hello, world!')\n```")
        
        # Verificar conversação
        self.assertEqual(len(self.agent.conversation.messages), 3)  # Sistema, usuário, assistente
        self.assertEqual(self.agent.conversation.messages[1].role, 'user')
        self.assertEqual(self.agent.conversation.messages[1].content, "Como imprimir 'Hello, world!' em Python?")
        self.assertEqual(self.agent.conversation.messages[2].role, 'assistant')
        self.assertEqual(self.agent.conversation.messages[2].content, response)
        
    def test_reset_conversation(self):
        """
        Testa o reset da conversação.
        """
        # Adicionar mensagens
        self.agent.conversation.add_user_message("Mensagem 1")
        self.agent.conversation.add_assistant_message("Resposta 1")
        
        # Verificar conversação
        self.assertEqual(len(self.agent.conversation.messages), 3)  # Sistema, usuário, assistente
        
        # Reset da conversação
        self.agent.reset_conversation()
        
        # Verificar conversação
        self.assertEqual(len(self.agent.conversation.messages), 1)  # Apenas sistema
        self.assertEqual(self.agent.conversation.messages[0].role, 'system')
        
    def test_memory(self):
        """
        Testa a memória do agente.
        """
        # Adicionar item à memória
        self.agent.remember("key1", "value1")
        
        # Verificar item
        self.assertEqual(self.agent.recall("key1"), "value1")
        
        # Verificar item inexistente
        self.assertIsNone(self.agent.recall("nonexistent"))
        
    def test_extract_tool_calls(self):
        """
        Testa a extração de chamadas de ferramentas.
        """
        # Texto com chamadas de ferramentas
        text = """
        Para listar os arquivos no diretório atual, você pode usar:
        
        {{tool.list_files(path=".")}}
        
        E para ler o conteúdo de um arquivo:
        
        {{tool.read_file(path="example.txt")}}
        """
        
        # Extrair chamadas
        tool_calls = self.agent._extract_tool_calls(text)
        
        # Verificar chamadas
        self.assertEqual(len(tool_calls), 2)
        
        self.assertEqual(tool_calls[0][0], "list_files")
        self.assertEqual(tool_calls[0][1], {"path": "."})
        
        self.assertEqual(tool_calls[1][0], "read_file")
        self.assertEqual(tool_calls[1][1], {"path": "example.txt"})
        
    def test_process_response_with_tool_calls(self):
        """
        Testa o processamento de respostas com chamadas de ferramentas.
        """
        # Configurar mock para a ferramenta
        tool = MagicMock()
        tool.execute.return_value = ["file1.txt", "file2.txt"]
        self.tool_registry.get.return_value = tool
        
        # Resposta com chamada de ferramenta
        response = "Para listar os arquivos no diretório atual:\n\n{{tool.list_files(path=\".\")}}\n\nEstes são os arquivos disponíveis."
        
        # Processar resposta
        processed_response = self.agent._process_response(response)
        
        # Verificar chamadas
        self.tool_registry.get.assert_called_once_with("list_files")
        tool.execute.assert_called_once_with(path=".")
        
        # Verificar resposta processada
        self.assertIn("{{tool.list_files result:}}", processed_response)
        self.assertIn("file1.txt", processed_response)
        self.assertIn("file2.txt", processed_response)
        
class TestConversation(unittest.TestCase):
    """
    Testes para a conversação.
    """
    
    def test_add_message(self):
        """
        Testa a adição de mensagens.
        """
        # Criar conversação
        conversation = Conversation()
        
        # Adicionar mensagens
        conversation.add_message(Message.system("Mensagem do sistema"))
        conversation.add_user_message("Mensagem do usuário")
        conversation.add_assistant_message("Mensagem do assistente")
        
        # Verificar mensagens
        self.assertEqual(len(conversation.messages), 3)
        self.assertEqual(conversation.messages[0].role, 'system')
        self.assertEqual(conversation.messages[0].content, "Mensagem do sistema")
        self.assertEqual(conversation.messages[1].role, 'user')
        self.assertEqual(conversation.messages[1].content, "Mensagem do usuário")
        self.assertEqual(conversation.messages[2].role, 'assistant')
        self.assertEqual(conversation.messages[2].content, "Mensagem do assistente")
        
    def test_get_messages_for_llm(self):
        """
        Testa a obtenção de mensagens para o LLM.
        """
        # Criar conversação
        conversation = Conversation()
        
        # Adicionar mensagens
        conversation.add_message(Message.system("Mensagem do sistema"))
        conversation.add_user_message("Mensagem do usuário")
        conversation.add_assistant_message("Mensagem do assistente")
        
        # Obter mensagens para o LLM
        messages = conversation.get_messages_for_llm()
        
        # Verificar mensagens
        self.assertEqual(len(messages), 3)
        self.assertEqual(messages[0]['role'], 'system')
        self.assertEqual(messages[0]['content'], "Mensagem do sistema")
        self.assertEqual(messages[1]['role'], 'user')
        self.assertEqual(messages[1]['content'], "Mensagem do usuário")
        self.assertEqual(messages[2]['role'], 'assistant')
        self.assertEqual(messages[2]['content'], "Mensagem do assistente")
        
    def test_get_last_message(self):
        """
        Testa a obtenção da última mensagem.
        """
        # Criar conversação
        conversation = Conversation()
        
        # Adicionar mensagens
        conversation.add_message(Message.system("Mensagem do sistema"))
        conversation.add_user_message("Mensagem do usuário")
        conversation.add_assistant_message("Mensagem do assistente")
        
        # Obter última mensagem
        last_message = conversation.get_last_message()
        
        # Verificar mensagem
        self.assertEqual(last_message.role, 'assistant')
        self.assertEqual(last_message.content, "Mensagem do assistente")
        
    def test_to_dict_from_dict(self):
        """
        Testa a conversão para e de dicionário.
        """
        # Criar conversação
        conversation = Conversation()
        
        # Adicionar mensagens
        conversation.add_message(Message.system("Mensagem do sistema"))
        conversation.add_user_message("Mensagem do usuário")
        conversation.add_assistant_message("Mensagem do assistente")
        
        # Converter para dicionário
        data = conversation.to_dict()
        
        # Criar nova conversação a partir do dicionário
        new_conversation = Conversation.from_dict(data)
        
        # Verificar mensagens
        self.assertEqual(len(new_conversation.messages), 3)
        self.assertEqual(new_conversation.messages[0].role, 'system')
        self.assertEqual(new_conversation.messages[0].content, "Mensagem do sistema")
        self.assertEqual(new_conversation.messages[1].role, 'user')
        self.assertEqual(new_conversation.messages[1].content, "Mensagem do usuário")
        self.assertEqual(new_conversation.messages[2].role, 'assistant')
        self.assertEqual(new_conversation.messages[2].content, "Mensagem do assistente")
        
if __name__ == "__main__":
    unittest.main()
