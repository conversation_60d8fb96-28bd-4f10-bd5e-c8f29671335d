#!/usr/bin/env python
"""
Executa todos os testes automatizados.
"""

import os
import sys
import unittest
import argparse

# Adicionar diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_tests(test_pattern=None, verbose=False):
    """
    Executa os testes.
    
    Args:
        test_pattern: Padrão para filtrar testes
        verbose: Se deve mostrar saída detalhada
    """
    # Descobrir testes
    loader = unittest.TestLoader()
    
    if test_pattern:
        suite = loader.loadTestsFromName(test_pattern)
    else:
        suite = loader.discover(os.path.dirname(os.path.abspath(__file__)))
    
    # Executar testes
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)
    
    # Retornar código de saída
    return 0 if result.wasSuccessful() else 1

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Executa testes automatizados")
    parser.add_argument("--pattern", help="Padrão para filtrar testes")
    parser.add_argument("--verbose", action="store_true", help="Mostrar saída detalhada")
    
    args = parser.parse_args()
    
    sys.exit(run_tests(args.pattern, args.verbose))
