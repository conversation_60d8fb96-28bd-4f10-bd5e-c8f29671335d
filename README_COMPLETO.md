# Augment Agent Auto - Versão Unificada

Este projeto é uma implementação local do Augment Agent Auto, similar ao disponível em [augmentcode.com](https://www.augmentcode.com). Ele integra um motor de contexto avançado, um modelo de linguagem local (LLM) e um conjunto de ferramentas para criar um assistente de programação poderoso.

## Arquivos Principais

- `cpunionagenteauto.py`: Script principal unificado que integra todos os componentes
- `executar_augment_auto.bat`: Script para configurar e executar o agente com interface CLI ou Web
- `executar_augment_auto_config.bat`: Script para executar o agente usando o arquivo de configuração
- `testar_augment_auto.bat`: Script para testar os componentes do agente
- `augment_auto_config.json`: Arquivo de configuração personalizada
- `README_AUGMENT_AUTO.md`: Documentação do projeto
- `README_COMPLETO.md`: Este arquivo com instruções detalhadas

## Requisitos

- Python 3.8 ou superior
- Modelo Claude 3.7 Sonnet Reasoning Gemma3 12B (ou outro modelo compatível com llama-cpp-python)
- Pelo menos 8GB de RAM (16GB recomendado)
- Espaço em disco para o modelo e índices (aproximadamente 10GB)

## Instalação

1. Clone ou baixe este repositório
2. Execute o script `executar_augment_auto.bat` para configurar o ambiente e iniciar o agente

O script irá:
- Criar um ambiente virtual Python
- Instalar as dependências necessárias
- Verificar se o modelo está disponível
- Iniciar o Augment Agent Auto com a interface escolhida

## Uso

### Iniciar o Agente

Existem várias maneiras de iniciar o Augment Agent Auto:

#### 1. Usando o script de execução padrão

```bash
executar_augment_auto.bat
```

Este script irá:
- Perguntar qual interface você deseja usar (CLI ou Web)
- Perguntar se você deseja indexar um codebase
- Iniciar o agente com as opções escolhidas

#### 2. Usando o script com configuração personalizada

```bash
executar_augment_auto_config.bat
```

Este script irá:
- Usar as configurações do arquivo `augment_auto_config.json`
- Perguntar se você deseja indexar um codebase
- Iniciar o agente com as configurações personalizadas

#### 3. Diretamente via Python

```bash
python cpunionagenteauto.py --ui cli --codebase "caminho/para/seu/projeto"
```

### Parâmetros de Linha de Comando

- `--ui`: Tipo de interface (`cli` ou `web`)
- `--codebase`: Caminho para o codebase a ser indexado
- `--model`: Caminho para o modelo LLM
- `--config`: Caminho para um arquivo de configuração personalizado
- `--log-level`: Nível de log (`DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`)

### Testar o Agente

Para verificar se todos os componentes estão funcionando corretamente:

```bash
testar_augment_auto.bat
```

Este script irá:
- Testar o motor de contexto
- Testar a integração com o LLM
- Testar as ferramentas disponíveis
- Testar o agente principal

## Estrutura do Projeto

O projeto é organizado em vários módulos:

### Módulos Principais

- `agent/`: Implementação do agente principal
  - `agent.py`: Classe principal do agente
  - `conversation.py`: Gerenciamento de conversas
  - `learning.py`: Sistema de aprendizado

- `context_engine/`: Motor de contexto para indexação e recuperação de código
  - `context_engine.py`: Classe principal do motor de contexto
  - `embedding.py`: Modelos de embedding para código
  - `indexing.py`: Indexação de codebase
  - `retrieval.py`: Recuperação de contexto relevante

- `llm_integration/`: Integração com modelos de linguagem
  - `local_llm.py`: Integração com LLM local usando llama-cpp-python
  - `unlimited_context.py`: Processador de contexto ilimitado
  - `llm_interface.py`: Interface comum para LLMs

- `tools/`: Ferramentas disponíveis para o agente
  - `code_tools/`: Ferramentas para manipulação de código
  - `file_tools/`: Ferramentas para manipulação de arquivos
  - `process_tools/`: Ferramentas para execução de processos
  - `search_tools/`: Ferramentas para busca na web

- `ui/`: Interfaces de usuário
  - `cli.py`: Interface de linha de comando
  - `web_ui.py`: Interface web
  - `web_templates/`: Templates HTML para a interface web

- `prompts/`: Prompts do sistema para o LLM
  - `master_prompt.txt`: Prompt principal do sistema

### Arquivos de Configuração

- `augment_auto_config.json`: Configuração personalizada
- `model_config.json`: Configuração do modelo LLM
- `config.json`: Configuração geral do agente

## Personalização

### Modelo de Linguagem

Por padrão, o agente usa o modelo Claude 3.7 Sonnet Reasoning Gemma3 12B. Você pode usar qualquer modelo compatível com a biblioteca llama-cpp-python, especificando o caminho com o parâmetro `--model` ou alterando o arquivo de configuração.

### Prompt do Sistema

O prompt do sistema está localizado em `prompts/master_prompt.txt`. Você pode modificá-lo para personalizar o comportamento do agente.

### Configuração

Você pode personalizar a configuração editando o arquivo `augment_auto_config.json`. As principais opções são:

- `llm`: Configurações do modelo de linguagem
  - `model_path`: Caminho para o modelo
  - `n_ctx`: Tamanho do contexto (em tokens)
  - `n_gpu_layers`: Número de camadas para executar na GPU (-1 para todas, 0 para nenhuma)
  - `temperature`: Temperatura para geração de texto

- `context_engine`: Configurações do motor de contexto
  - `embedding_dim`: Dimensão dos embeddings
  - `max_results`: Número máximo de resultados a retornar
  - `supported_extensions`: Extensões de arquivo suportadas

- `agent`: Configurações do agente
  - `max_context_results`: Número máximo de resultados de contexto
  - `max_conversation_history`: Número máximo de mensagens no histórico

- `ui`: Configurações da interface
  - `type`: Tipo de interface (`cli` ou `web`)
  - `web`: Configurações da interface web

## Ferramentas Disponíveis

O Augment Agent Auto inclui as seguintes categorias de ferramentas:

### Ferramentas de Código

- Análise de código
- Formatação de código
- Geração de código
- Refatoração de código

### Ferramentas de Arquivo

- Leitura de arquivos
- Escrita de arquivos
- Listagem de diretórios
- Busca de arquivos

### Ferramentas de Busca

- Busca na web
- Busca no codebase
- Recuperação de documentação

### Ferramentas de Processo

- Execução de comandos
- Gerenciamento de processos

## Solução de Problemas

### Modelo não encontrado

Se o modelo não for encontrado no caminho padrão, você pode especificar um caminho diferente usando o parâmetro `--model` ou editando o arquivo de configuração.

### Erro de memória

Se você encontrar erros de memória, tente:
1. Reduzir o valor de `n_ctx` na configuração
2. Usar um modelo menor ou mais quantizado
3. Fechar outros aplicativos que consomem muita memória

### Problemas de indexação

Se a indexação do codebase falhar, verifique:
1. Se o caminho está correto
2. Se você tem permissões de leitura para os arquivos
3. Se o codebase não é muito grande (considere indexar apenas partes relevantes)

### Problemas com a interface web

Se a interface web não funcionar, verifique:
1. Se as dependências `flask` e `flask-socketio` estão instaladas
2. Se a porta 5000 está disponível
3. Se você tem permissões para abrir a porta

## Desenvolvimento

### Adicionar novas ferramentas

Para adicionar uma nova ferramenta:

1. Crie uma nova classe que herde de `tools.tool_interface.Tool`
2. Implemente os métodos `execute` e `get_description`
3. Registre a ferramenta no `ToolRegistry`

### Personalizar o prompt do sistema

Para personalizar o prompt do sistema:

1. Edite o arquivo `prompts/master_prompt.txt`
2. Reinicie o agente para que as alterações tenham efeito

### Melhorar o motor de contexto

Para melhorar o motor de contexto:

1. Edite os arquivos em `context_engine/`
2. Considere usar modelos de embedding mais avançados
3. Ajuste os parâmetros de indexação e recuperação

## Contribuição

Contribuições são bem-vindas! Sinta-se à vontade para abrir issues ou pull requests.

## Licença

Este projeto é licenciado sob a licença MIT.
