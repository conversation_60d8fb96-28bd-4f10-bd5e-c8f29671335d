"""
Integração com o modelo Gema LLM

Este módulo implementa a integração com o modelo Gema LLM.
"""

import os
import json
import logging
import requests
import time
from typing import Dict, List, Any, Optional, Union, Tuple

from .llm_interface import LLMInterface

# Configurar logger
logger = logging.getLogger("augment.llm.gema")

class GemaLLM(LLMInterface):
    """
    Integração com o modelo Gema LLM.
    """

    def __init__(self, api_url: Optional[str] = None, api_key: Optional[str] = None,
                 model: str = "gema", config: Optional[Dict[str, Any]] = None):
        """
        Inicializa a integração com o Gema LLM.

        Args:
            api_url: URL da API do Gema (opcional)
            api_key: Chave de API do Gema (opcional)
            model: Nome do modelo a ser usado
            config: Configurações adicionais
        """
        self.config = config or {}

        # Configurações da API
        self.api_url = api_url or os.environ.get("GEMA_API_URL", "http://localhost:8000/v1")
        self.api_key = api_key or os.environ.get("GEMA_API_KEY", "")
        self.model = model

        # Modo de simulação (usado quando o servidor não está disponível)
        self.simulation_mode = self.config.get("simulation_mode", False)

        # Configurações padrão para geração
        self.default_params = {
            "temperature": self.config.get("temperature", 0.7),
            "max_tokens": self.config.get("max_tokens", 1024),
            "top_p": self.config.get("top_p", 0.95),
            "frequency_penalty": self.config.get("frequency_penalty", 0.0),
            "presence_penalty": self.config.get("presence_penalty", 0.0),
            "stop": self.config.get("stop", None)
        }

        # Estatísticas
        self.stats = {
            "total_requests": 0,
            "total_tokens": 0,
            "total_time": 0.0,
            "errors": 0
        }

        logger.info(f"Inicializando integração com Gema LLM: modelo={model}, api_url={self.api_url}")

    def generate(self, prompt: str, **kwargs) -> str:
        """
        Gera texto a partir de um prompt.

        Args:
            prompt: Prompt para geração
            **kwargs: Parâmetros adicionais para o modelo

        Returns:
            Texto gerado
        """
        # Preparar parâmetros
        params = self.default_params.copy()
        params.update(kwargs)

        # Preparar payload
        payload = {
            "model": self.model,
            "prompt": prompt,
            **params
        }

        # Fazer requisição
        start_time = time.time()
        try:
            response = self._make_request("completions", payload)

            # Extrair texto gerado
            generated_text = response.get("choices", [{}])[0].get("text", "")

            # Atualizar estatísticas
            self.stats["total_requests"] += 1
            self.stats["total_tokens"] += response.get("usage", {}).get("total_tokens", 0)
            self.stats["total_time"] += time.time() - start_time

            return generated_text
        except Exception as e:
            logger.error(f"Erro ao gerar texto: {e}")
            self.stats["errors"] += 1
            return ""

    def generate_with_context(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """
        Gera texto a partir de um prompt e contexto.

        Args:
            prompt: Prompt para geração
            context: Lista de snippets de contexto
            **kwargs: Parâmetros adicionais para o modelo

        Returns:
            Texto gerado
        """
        # Formatar contexto
        formatted_context = self._format_context(context)

        # Combinar prompt e contexto
        combined_prompt = f"{formatted_context}\n\n{prompt}"

        # Gerar texto
        return self.generate(combined_prompt, **kwargs)

    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        Gera uma resposta para uma conversa.

        Args:
            messages: Lista de mensagens da conversa
            **kwargs: Parâmetros adicionais para o modelo

        Returns:
            Resposta gerada
        """
        # Preparar parâmetros
        params = self.default_params.copy()
        params.update(kwargs)

        # Preparar payload
        payload = {
            "model": self.model,
            "messages": messages,
            **params
        }

        # Fazer requisição
        start_time = time.time()
        try:
            response = self._make_request("chat/completions", payload)

            # Extrair texto gerado
            generated_text = response.get("choices", [{}])[0].get("message", {}).get("content", "")

            # Atualizar estatísticas
            self.stats["total_requests"] += 1
            self.stats["total_tokens"] += response.get("usage", {}).get("total_tokens", 0)
            self.stats["total_time"] += time.time() - start_time

            return generated_text
        except Exception as e:
            logger.error(f"Erro ao gerar resposta de chat: {e}")
            self.stats["errors"] += 1
            return ""

    def chat_with_context(self, messages: List[Dict[str, str]], context: List[Dict[str, Any]], **kwargs) -> str:
        """
        Gera uma resposta para uma conversa com contexto.

        Args:
            messages: Lista de mensagens da conversa
            context: Lista de snippets de contexto
            **kwargs: Parâmetros adicionais para o modelo

        Returns:
            Resposta gerada
        """
        # Formatar contexto
        formatted_context = self._format_context(context)

        # Adicionar contexto como mensagem do sistema
        system_message = {
            "role": "system",
            "content": f"Você tem acesso ao seguinte contexto relevante:\n\n{formatted_context}"
        }

        # Verificar se já existe uma mensagem do sistema
        if messages and messages[0].get("role") == "system":
            # Combinar com a mensagem do sistema existente
            messages[0]["content"] = f"{messages[0]['content']}\n\n{system_message['content']}"
        else:
            # Adicionar nova mensagem do sistema
            messages = [system_message] + messages

        # Gerar resposta
        return self.chat(messages, **kwargs)

    def _make_request(self, endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Faz uma requisição para a API do Gema.

        Args:
            endpoint: Endpoint da API
            payload: Payload da requisição

        Returns:
            Resposta da API
        """
        url = f"{self.api_url}/{endpoint}"

        headers = {
            "Content-Type": "application/json"
        }

        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        try:
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro na requisição para {url}: {e}")
            raise

    def _format_context(self, context: List[Dict[str, Any]]) -> str:
        """
        Formata o contexto para inclusão no prompt.

        Args:
            context: Lista de snippets de contexto

        Returns:
            Contexto formatado
        """
        formatted_snippets = []

        for i, snippet in enumerate(context):
            content = snippet.get("content", "")
            file_path = snippet.get("file_path", "unknown")
            start_line = snippet.get("start_line", 1)
            end_line = snippet.get("end_line", start_line + content.count("\n"))
            language = snippet.get("language", "")

            formatted_snippet = f"[SNIPPET {i+1}] {file_path}:{start_line}-{end_line} ({language})\n```{language}\n{content}\n```"
            formatted_snippets.append(formatted_snippet)

        return "\n\n".join(formatted_snippets)

    def get_model_info(self) -> Dict[str, Any]:
        """
        Obtém informações sobre o modelo.

        Returns:
            Informações sobre o modelo
        """
        return {
            "name": self.model,
            "api_url": self.api_url,
            "stats": self.stats
        }
