"""
Implementação de fallback para ferramentas de processo.

Este módulo fornece implementações alternativas para as ferramentas de processo
quando as dependências necessárias não estão disponíveis.

Complexidade:
- Tempo: O(1) para a maioria das operações (simulações)
- Espaço: O(1) - dados estáticos
"""

import os
import sys
import subprocess
import logging
import time
from typing import Dict, List, Any, Optional, Union, Tuple

# Configurar logger
logger = logging.getLogger("tools.process.fallback")

class FallbackProcessTools:
    """
    Implementação de fallback para ferramentas de processo.
    
    Esta classe fornece implementações simuladas das ferramentas de processo
    quando as dependências necessárias não estão disponíveis.
    
    Complexidade:
        Tempo: O(1) para a maioria das operações (simulações)
        Espaço: O(1) para metadados
    """
    
    def __init__(self):
        """
        Inicializa as ferramentas de processo de fallback.
        """
        logger.warning("Usando implementação de fallback para ferramentas de processo")
        logger.warning("Funcionalidade limitada: instale 'psutil' para funcionalidade completa")
        
        # Mensagem de fallback
        self.fallback_message = (
            "Não foi possível realizar a operação de processo porque a biblioteca psutil "
            "não está instalada. Por favor, instale-a usando 'pip install psutil'."
        )
        
        # Processos simulados
        self._simulated_processes = {}
        self._next_pid = 1000
    
    def list_processes(self) -> List[Dict[str, Any]]:
        """
        Simula a listagem de processos em execução.
        
        Returns:
            Lista de processos simulados
            
        Complexidade:
            Tempo: O(1) - operação simulada
            Espaço: O(1) - dados estáticos
        """
        logger.warning("Listagem de processos simulada")
        
        # Processos básicos do sistema
        basic_processes = [
            {"pid": 1, "name": "System", "status": "running", "cpu_percent": 0.1, "memory_percent": 0.5},
            {"pid": 2, "name": "Explorer", "status": "running", "cpu_percent": 1.0, "memory_percent": 2.0},
            {"pid": 4, "name": "Python", "status": "running", "cpu_percent": 5.0, "memory_percent": 10.0}
        ]
        
        # Adicionar processos simulados
        processes = basic_processes + list(self._simulated_processes.values())
        
        return processes
    
    def get_process_info(self, pid: int) -> Dict[str, Any]:
        """
        Simula a obtenção de informações sobre um processo.
        
        Args:
            pid: ID do processo
            
        Returns:
            Informações simuladas sobre o processo
            
        Complexidade:
            Tempo: O(1) - operação simulada
            Espaço: O(1) - dados estáticos
        """
        logger.warning(f"Obtenção de informações de processo simulada para PID {pid}")
        
        # Verificar se é um processo simulado
        if pid in self._simulated_processes:
            return self._simulated_processes[pid]
        
        # Processos básicos do sistema
        if pid == 1:
            return {"pid": 1, "name": "System", "status": "running", "cpu_percent": 0.1, "memory_percent": 0.5}
        elif pid == 2:
            return {"pid": 2, "name": "Explorer", "status": "running", "cpu_percent": 1.0, "memory_percent": 2.0}
        elif pid == 4:
            return {"pid": 4, "name": "Python", "status": "running", "cpu_percent": 5.0, "memory_percent": 10.0}
        
        # Processo não encontrado
        return {"pid": pid, "name": "Unknown", "status": "not found", "error": "Process not found"}
    
    def execute_command(self, command: str, shell: bool = True, timeout: Optional[float] = None) -> Dict[str, Any]:
        """
        Executa um comando do sistema operacional usando subprocess.
        
        Esta função é implementada mesmo no fallback porque não depende de psutil.
        
        Args:
            command: Comando a ser executado
            shell: Se True, executa o comando em um shell
            timeout: Tempo máximo de execução em segundos
            
        Returns:
            Resultado da execução do comando
            
        Complexidade:
            Tempo: O(n) onde n é o tempo de execução do comando
            Espaço: O(m) onde m é o tamanho da saída do comando
        """
        logger.info(f"Executando comando: {command}")
        
        try:
            # Executar o comando
            process = subprocess.run(
                command,
                shell=shell,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            # Criar processo simulado
            pid = self._next_pid
            self._next_pid += 1
            
            # Registrar processo simulado
            self._simulated_processes[pid] = {
                "pid": pid,
                "name": f"Command_{pid}",
                "status": "terminated",
                "cpu_percent": 0.0,
                "memory_percent": 0.0,
                "command": command,
                "returncode": process.returncode,
                "stdout": process.stdout,
                "stderr": process.stderr
            }
            
            # Retornar resultado
            return {
                "pid": pid,
                "returncode": process.returncode,
                "stdout": process.stdout,
                "stderr": process.stderr,
                "success": process.returncode == 0
            }
        
        except subprocess.TimeoutExpired:
            logger.warning(f"Comando atingiu o timeout: {command}")
            return {
                "pid": None,
                "returncode": -1,
                "stdout": "",
                "stderr": f"Timeout após {timeout} segundos",
                "success": False,
                "error": "timeout"
            }
        
        except Exception as e:
            logger.error(f"Erro ao executar comando: {e}")
            return {
                "pid": None,
                "returncode": -1,
                "stdout": "",
                "stderr": str(e),
                "success": False,
                "error": str(e)
            }
    
    def kill_process(self, pid: int) -> bool:
        """
        Simula a terminação de um processo.
        
        Args:
            pid: ID do processo
            
        Returns:
            True se o processo foi terminado, False caso contrário
            
        Complexidade:
            Tempo: O(1) - operação simulada
            Espaço: O(1) - dados estáticos
        """
        logger.warning(f"Terminação de processo simulada para PID {pid}")
        
        # Verificar se é um processo simulado
        if pid in self._simulated_processes:
            self._simulated_processes[pid]["status"] = "terminated"
            return True
        
        # Não é possível terminar processos do sistema
        if pid in [1, 2, 4]:
            return False
        
        # Processo não encontrado
        return False
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        Simula a obtenção de informações sobre o sistema.
        
        Returns:
            Informações simuladas sobre o sistema
            
        Complexidade:
            Tempo: O(1) - operação simulada
            Espaço: O(1) - dados estáticos
        """
        logger.warning("Obtenção de informações do sistema simulada")
        
        return {
            "cpu_count": os.cpu_count() or 4,
            "cpu_percent": 25.0,
            "memory_total": 16 * 1024 * 1024 * 1024,  # 16 GB
            "memory_available": 8 * 1024 * 1024 * 1024,  # 8 GB
            "memory_percent": 50.0,
            "disk_total": 512 * 1024 * 1024 * 1024,  # 512 GB
            "disk_free": 256 * 1024 * 1024 * 1024,  # 256 GB
            "disk_percent": 50.0,
            "boot_time": time.time() - 86400,  # 1 dia atrás
            "python_version": sys.version,
            "platform": sys.platform
        }
