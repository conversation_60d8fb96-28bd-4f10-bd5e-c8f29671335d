# Gema Agent (Estilo Augment)

Este projeto implementa o Gema Agent no estilo do Augment Agent Auto, com suporte a programação de alta qualidade seguindo as diretrizes mestras para geração de código.

## Características Principais

- **Estilo Augment Agent Auto**: O Gema Agent funciona exatamente como o Augment Agent Auto, mostrando caixas de diálogo durante o processo de execução.
- **Programação de Alta Qualidade**: O Gema Agent segue as diretrizes mestras para geração de código de alta performance e qualidade científica.
- **Suporte a Unicode**: O Gema Agent tem suporte completo a Unicode e caracteres especiais.
- **Correção de Quebras de Linha**: O Gema Agent corrige automaticamente problemas com quebras de linha em textos gerados por modelos de linguagem.
- **Correção de Código Python**: O Gema Agent corrige automaticamente problemas em código Python gerado por modelos de linguagem.
- **Etapas de Programação**: O Gema Agent divide o processo de programação em etapas bem definidas, como análise, planejamento, implementação, testes e documentação.
- **Contexto de Programação**: O Gema Agent mantém um contexto de programação com informações sobre o projeto atual, arquivos, dependências, etc.

## Módulos Principais

### 1. `gema_augment_style.py`

Classe principal do Gema Agent no estilo Augment. Processa mensagens do usuário, executa ferramentas e gera respostas.

### 2. `line_break_fixer.py`

Módulo especializado para correção de quebras de linha em textos gerados por modelos de linguagem.

### 3. `python_code_fixer.py`

Módulo especializado para correção de código Python corrompido gerado por modelos de linguagem.

### 4. `programming_steps.py`

Módulo para gerenciar as etapas do processo de programação, como análise, planejamento, implementação, testes e documentação.

### 5. `programming_context.py`

Módulo para gerenciar o contexto de programação, incluindo informações sobre o projeto atual, arquivos, dependências, etc.

### 6. `tool_prompts.py`

Módulo com prompts para as ferramentas do Gema Agent no estilo Augment Agent Auto.

### 7. `encoding_tools.py`

Módulo com ferramentas para lidar com codificação de caracteres, incluindo detecção automática de codificação, conversão de codificação, etc.

### 8. `file_tools.py`

Módulo com ferramentas para lidar com arquivos, incluindo listagem de arquivos, remoção de arquivos, cópia de arquivos, etc.

### 9. `process_tools.py`

Módulo com ferramentas para lidar com processos, incluindo execução de comandos, etc.

### 10. `search_tools.py`

Módulo com ferramentas para busca na web.

## Ferramentas Disponíveis

1. `write_file(path, content, encoding='utf-8')` - Cria ou sobrescreve um arquivo com suporte completo a Unicode e caracteres especiais
2. `read_file(path, encoding=None)` - Lê o conteúdo de um arquivo com detecção automática de codificação
3. `list_files(path='.', pattern=None)` - Lista arquivos em um diretório
4. `remove_file(path)` - Remove um arquivo
5. `copy_file(source, destination, overwrite=False)` - Copia um arquivo
6. `execute_command(command)` - Executa um comando do sistema
7. `web_search(query)` - Busca informações na web
8. `encode_base64(text)` - Codifica texto em base64, preservando caracteres Unicode
9. `decode_base64(base64_text)` - Decodifica texto de base64 para Unicode
10. `debug_string(text)` - Retorna informações de debug sobre uma string
11. `write_file_unicode(path, content, encoding='utf-8', bom=False)` - Escreve conteúdo em um arquivo com suporte a BOM
12. `read_file_unicode(path, encoding=None)` - Lê o conteúdo de um arquivo com informações detalhadas de codificação
13. `normalize_unicode(text, form='NFC')` - Normaliza um texto Unicode para uma forma específica
14. `detect_encoding(text_bytes)` - Detecta a codificação de um texto em bytes
15. `convert_encoding(text, source_encoding, target_encoding='utf-8')` - Converte um texto de uma codificação para outra
16. `get_supported_encodings()` - Retorna uma lista de codificações suportadas pelo sistema

## Correções Implementadas

### 1. Correção de Quebras de Linha

O módulo `line_break_fixer.py` foi implementado para corrigir problemas com quebras de linha em textos gerados por modelos de linguagem. Ele detecta e corrige diferentes tipos de problemas, como:

- Substituição de 'n' literal por '\n'
- Substituição de sequências de escape '\\n' por quebras de linha reais
- Normalização de diferentes tipos de quebras de linha
- Tratamento especial para padrões comuns como "Linha 1nLinha 2"

### 2. Correção de Código Python

O módulo `python_code_fixer.py` foi implementado para corrigir problemas em código Python gerado por modelos de linguagem. Ele detecta e corrige diferentes tipos de problemas, como:

- Strings não terminadas
- Palavras-chave corrompidas
- Indentação incorreta
- Quebras de linha incorretas

### 3. Integração de Etapas de Programação

O módulo `programming_steps.py` foi implementado para gerenciar as etapas do processo de programação. Ele divide o processo em etapas bem definidas, como:

- Análise do problema
- Planejamento da solução
- Implementação
- Testes
- Documentação
- Conclusão

### 4. Integração de Contexto de Programação

O módulo `programming_context.py` foi implementado para gerenciar o contexto de programação. Ele mantém informações sobre o projeto atual, como:

- Linguagem de programação
- Framework
- Dependências
- Arquivos
- Banco de dados
- APIs
- Variáveis de ambiente

### 5. Integração de Prompts para Ferramentas

O módulo `tool_prompts.py` foi implementado para gerenciar os prompts para as ferramentas do Gema Agent no estilo Augment Agent Auto. Ele fornece prompts para:

- Entrada de ferramentas
- Saída de ferramentas
- Erros de ferramentas
- Etapas de programação

## Como Usar

### Execução Direta

```bash
python gema_augment_style.py --message "Sua mensagem aqui"
```

### Modo Interativo

```bash
python gema_augment_style.py --interactive
```

### Script Batch

```bash
run_gema_corrected.bat "Sua mensagem aqui"
```

## Testes

Para testar o Gema Agent, execute:

```bash
python test_gema_agent.py
```

Este script executa uma série de testes para verificar se o Gema Agent está funcionando corretamente com todas as correções implementadas.

## Diretrizes Mestras para Geração de Código

O Gema Agent segue as diretrizes mestras para geração de código de alta performance e qualidade científica, incluindo:

1. **Análise de Complexidade Assintótica Rigorosa**: Determina a complexidade de tempo (Big O) e espaço do algoritmo antes da implementação.
2. **Minimização Extrema de Alocações e Acessos à Memória**: Reduz alocações dinâmicas ao mínimo absoluto.
3. **Imutabilidade e Funções Puras como Padrão**: Adota imutabilidade para estruturas de dados sempre que possível.
4. **Previsibilidade de Fluxo para Otimização de CPU**: Estrutura condicionais e loops para maximizar a previsibilidade pelo processador.
5. **Design Orientado a Dados e Otimização de Layout**: Organiza os dados conforme os padrões de acesso para otimizar o uso de cache.
6. **Abstrações de Custo Zero ou Mínimo**: Utiliza abstrações de alto nível somente se o compilador puder otimizá-las completamente.
7. **Validação de Contratos e Defesa Proativa**: Implementa validação rigorosa de todas as entradas externas e pré-condições de funções.
8. **Princípio DRY (Don't Repeat Yourself) Absoluto**: Elimina toda e qualquer redundância de código e lógica.
9. **Código Conciso, Expressivo e Auto-Documentado**: Escreve o código mais curto e direto possível que ainda mantenha clareza cristalina.
10. **Tratamento de Erros Explícito e Robusto**: Nunca ignora erros potenciais.
11. **Preservação de Código Funcional e Minimização de Refatoração Invasiva**: Não modifica código existente que está comprovadamente funcional e testado.
12. **Segurança por Design e Padrão**: Incorpora a segurança como requisito fundamental desde o início.
13. **Testabilidade Intrínseca**: Projeta cada unidade de código para ser facilmente testável de forma isolada.
14. **Escalabilidade e Concorrência Consciente**: Considera o impacto de suas escolhas de design na escalabilidade horizontal e vertical.
15. **Otimização de I/O e Recursos Externos**: Minimiza a frequência e a latência de operações de I/O.
16. **Conformidade com Idiomatismos e Melhores Práticas da Linguagem**: Adota as convenções idiomáticas, padrões de design e guias de estilo específicos da linguagem de programação em uso.
17. **Eliminação de Código Morto e Variáveis Não Utilizadas**: Garante que todo código gerado seja alcançável e que todas as variáveis declaradas sejam utilizadas.
18. **Gestão de Dependências Clara e Mínima**: Utiliza o mínimo de dependências externas necessárias.
19. **Perfilagem e Otimização Baseada em Evidências**: Não realiza otimizações prematuras.
20. **Aprendizado Contínuo e Prevenção de Regressão de Qualidade**: Evita ativamente a reintrodução de antipadrões ou bugs que foram corrigidos ou identificados em interações anteriores.
