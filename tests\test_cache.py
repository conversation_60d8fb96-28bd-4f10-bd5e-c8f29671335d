"""
Testes para o Sistema de Cache

Este módulo contém testes para o sistema de cache.
"""

import os
import sys
import unittest
import tempfile
import shutil
import time
from pathlib import Path

# Adicionar diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.cache import MemoryCache, DiskCache, MultiLevelCache

class TestMemoryCache(unittest.TestCase):
    """
    Testes para o cache em memória.
    """
    
    def setUp(self):
        """
        Configuração para os testes.
        """
        self.cache = MemoryCache(max_size=5, ttl=1)
        
    def test_set_get(self):
        """
        Testa as operações básicas de set e get.
        """
        # Definir valor
        self.cache.set("key1", "value1")
        
        # Obter valor
        value = self.cache.get("key1")
        
        # Verificar valor
        self.assertEqual(value, "value1")
        
    def test_get_nonexistent(self):
        """
        Testa a obtenção de uma chave inexistente.
        """
        value = self.cache.get("nonexistent")
        self.assertIsNone(value)
        
    def test_max_size(self):
        """
        Testa o limite de tamanho do cache.
        """
        # Definir valores
        for i in range(10):
            self.cache.set(f"key{i}", f"value{i}")
            
        # Verificar tamanho
        self.assertEqual(len(self.cache.cache), 5)
        
        # Verificar que as chaves mais antigas foram removidas
        self.assertIsNone(self.cache.get("key0"))
        self.assertIsNone(self.cache.get("key1"))
        self.assertIsNone(self.cache.get("key2"))
        self.assertIsNone(self.cache.get("key3"))
        self.assertIsNone(self.cache.get("key4"))
        
        # Verificar que as chaves mais recentes foram mantidas
        self.assertEqual(self.cache.get("key5"), "value5")
        self.assertEqual(self.cache.get("key6"), "value6")
        self.assertEqual(self.cache.get("key7"), "value7")
        self.assertEqual(self.cache.get("key8"), "value8")
        self.assertEqual(self.cache.get("key9"), "value9")
        
    def test_ttl(self):
        """
        Testa o tempo de vida dos itens.
        """
        # Definir valor
        self.cache.set("key1", "value1")
        
        # Verificar valor
        self.assertEqual(self.cache.get("key1"), "value1")
        
        # Esperar pelo TTL
        time.sleep(1.1)
        
        # Verificar que o valor expirou
        self.assertIsNone(self.cache.get("key1"))
        
    def test_clear(self):
        """
        Testa a limpeza do cache.
        """
        # Definir valores
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        
        # Limpar cache
        self.cache.clear()
        
        # Verificar que os valores foram removidos
        self.assertIsNone(self.cache.get("key1"))
        self.assertIsNone(self.cache.get("key2"))
        
class TestDiskCache(unittest.TestCase):
    """
    Testes para o cache em disco.
    """
    
    def setUp(self):
        """
        Configuração para os testes.
        """
        self.temp_dir = tempfile.mkdtemp()
        self.cache = DiskCache(cache_dir=self.temp_dir, ttl=1, max_size_mb=1)
        
    def tearDown(self):
        """
        Limpeza após os testes.
        """
        shutil.rmtree(self.temp_dir)
        
    def test_set_get(self):
        """
        Testa as operações básicas de set e get.
        """
        # Definir valor
        self.cache.set("key1", "value1")
        
        # Obter valor
        value = self.cache.get("key1")
        
        # Verificar valor
        self.assertEqual(value, "value1")
        
    def test_get_nonexistent(self):
        """
        Testa a obtenção de uma chave inexistente.
        """
        value = self.cache.get("nonexistent")
        self.assertIsNone(value)
        
    def test_ttl(self):
        """
        Testa o tempo de vida dos itens.
        """
        # Definir valor
        self.cache.set("key1", "value1")
        
        # Verificar valor
        self.assertEqual(self.cache.get("key1"), "value1")
        
        # Esperar pelo TTL
        time.sleep(1.1)
        
        # Verificar que o valor expirou
        self.assertIsNone(self.cache.get("key1"))
        
    def test_clear(self):
        """
        Testa a limpeza do cache.
        """
        # Definir valores
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        
        # Limpar cache
        self.cache.clear()
        
        # Verificar que os valores foram removidos
        self.assertIsNone(self.cache.get("key1"))
        self.assertIsNone(self.cache.get("key2"))
        
class TestMultiLevelCache(unittest.TestCase):
    """
    Testes para o cache em múltiplos níveis.
    """
    
    def setUp(self):
        """
        Configuração para os testes.
        """
        self.temp_dir = tempfile.mkdtemp()
        self.cache = MultiLevelCache(
            memory_cache_size=5,
            memory_ttl=1,
            disk_cache_dir=self.temp_dir,
            disk_ttl=2,
            disk_max_size_mb=1
        )
        
    def tearDown(self):
        """
        Limpeza após os testes.
        """
        shutil.rmtree(self.temp_dir)
        
    def test_set_get(self):
        """
        Testa as operações básicas de set e get.
        """
        # Definir valor
        self.cache.set("key1", "value1")
        
        # Obter valor
        value = self.cache.get("key1")
        
        # Verificar valor
        self.assertEqual(value, "value1")
        
    def test_get_nonexistent(self):
        """
        Testa a obtenção de uma chave inexistente.
        """
        value = self.cache.get("nonexistent")
        self.assertIsNone(value)
        
    def test_memory_to_disk(self):
        """
        Testa a passagem de valores da memória para o disco.
        """
        # Definir valor
        self.cache.set("key1", "value1")
        
        # Verificar valor na memória
        self.assertEqual(self.cache.memory_cache.get("key1"), "value1")
        
        # Esperar pelo TTL da memória
        time.sleep(1.1)
        
        # Verificar que o valor não está mais na memória
        self.assertIsNone(self.cache.memory_cache.get("key1"))
        
        # Verificar que o valor ainda está no disco
        self.assertEqual(self.cache.disk_cache.get("key1"), "value1")
        
        # Verificar que o valor ainda pode ser obtido pelo cache multinível
        self.assertEqual(self.cache.get("key1"), "value1")
        
        # Verificar que o valor foi recarregado na memória
        self.assertEqual(self.cache.memory_cache.get("key1"), "value1")
        
    def test_clear(self):
        """
        Testa a limpeza do cache.
        """
        # Definir valores
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        
        # Limpar cache
        self.cache.clear()
        
        # Verificar que os valores foram removidos
        self.assertIsNone(self.cache.get("key1"))
        self.assertIsNone(self.cache.get("key2"))
        
if __name__ == "__main__":
    unittest.main()
