"""
Ferramentas de Busca

Este módulo implementa ferramentas para busca na web e em outras fontes.
Inclui mecanismo de fallback para quando as dependências não estão disponíveis.

Complexidade:
- Tempo: Depende da implementação específica (O(1) para fallback, O(n) para implementação real)
- Espaço: O(1) para metadados, O(n) para resultados de busca
"""

import logging
import importlib.util

# Configurar logger
logger = logging.getLogger("tools.search")

# Verificar se BeautifulSoup está disponível
bs4_available = importlib.util.find_spec("bs4") is not None

try:
    if bs4_available:
        # Importar implementação real
        from .search_tools import SearchTools
        logger.info("Usando implementação completa das ferramentas de busca")
    else:
        # Importar implementação de fallback
        from .fallback_search import FallbackSearchTools as SearchTools
        logger.warning("Usando implementação de fallback das ferramentas de busca")
except ImportError as e:
    # Em caso de erro, usar implementação de fallback
    logger.error(f"Erro ao importar ferramentas de busca: {e}")
    from .fallback_search import FallbackSearchTools as SearchTools
    logger.warning("Usando implementação de fallback das ferramentas de busca devido a erro")

__all__ = ['SearchTools']
