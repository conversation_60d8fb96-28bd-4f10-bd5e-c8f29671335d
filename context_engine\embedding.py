"""
Modelo de Embedding

Este módulo implementa o modelo de embedding que converte código e consultas
em representações vetoriais usando técnicas avançadas de machine learning.
"""

import os
import hashlib
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Union
import time

# Configurar logger
logger = logging.getLogger("augment.embedding")

class EmbeddingModel:
    """
    Modelo de embedding avançado que converte texto em vetores usando
    modelos pré-treinados especializados para código.
    """

    # Modelos utilizados pelo Augment Agent real
    AUGMENT_MODELS = {
        'code_primary': 'Salesforce/codet5p-220m',
        'code_secondary': 'microsoft/codebert-base',
        'text_primary': 'sentence-transformers/all-mpnet-base-v2',
        'text_secondary': 'sentence-transformers/all-MiniLM-L6-v2',
        'multilingual': 'sentence-transformers/paraphrase-multilingual-mpnet-base-v2',
        # Modelos locais GGUF
        'local_primary': 'C:\\Users\\<USER>\\.lmstudio\\models\\nomic-ai\\nomic-embed-text-v2-moe-GGUF\\nomic-embed-text.gguf',
        # Modelos específicos por linguagem
        'python': 'Salesforce/codet5p-220m-py',
        'javascript': 'Salesforce/codet5p-220m-js',
        'typescript': 'Salesforce/codet5p-220m-js',
        'java': 'Salesforce/codet5p-220m-java',
        'go': 'Salesforce/codet5p-220m-go',
        'ruby': 'Salesforce/codet5p-220m',
        'php': 'Salesforce/codet5p-220m-php',
        'c': 'Salesforce/codet5p-220m',
        'cpp': 'Salesforce/codet5p-220m',
        'csharp': 'Salesforce/codet5p-220m-csharp',
        'rust': 'Salesforce/codet5p-220m',
        'html': 'Salesforce/codet5p-220m-html',
        'css': 'Salesforce/codet5p-220m',
        'sql': 'Salesforce/codet5p-220m',
        'kotlin': 'Salesforce/codet5p-220m-java',
        'swift': 'Salesforce/codet5p-220m',
        'scala': 'Salesforce/codet5p-220m-java',
        'dart': 'Salesforce/codet5p-220m',
        'r': 'Salesforce/codet5p-220m',
        'shell': 'Salesforce/codet5p-220m',
        'powershell': 'Salesforce/codet5p-220m',
    }

    def __init__(self, model_path=None, embedding_dim=768, model_type="code",
                 language=None, cache=None, config=None):
        """
        Inicializa o modelo de embedding.

        Args:
            model_path: Caminho para o modelo pré-treinado
            embedding_dim: Dimensão do embedding (768 para CodeBERT, 384 para MiniLM)
            model_type: Tipo de modelo ('code', 'text', 'multilingual', 'local')
            language: Linguagem de programação específica
            cache: Cache para armazenar embeddings
            config: Configurações adicionais
        """
        self.config = config or {}
        self.model_path = model_path or self.AUGMENT_MODELS.get('local_primary')
        self.embedding_dim = embedding_dim
        self.model_type = model_type
        self.language = language
        self.cache = cache

        # Modelos primário e secundário
        self.primary_model = None
        self.secondary_model = None
        self.primary_tokenizer = None
        self.secondary_tokenizer = None

        # Estatísticas
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'primary_model_uses': 0,
            'secondary_model_uses': 0,
            'fallback_uses': 0
        }

        # Tentar carregar modelos avançados se disponíveis
        logger.info(f"Inicializando modelo de embedding: tipo={model_type}, linguagem={language}")
        self._load_models()

    def _load_models(self):
        """
        Carrega os modelos de embedding primário e secundário com base nas bibliotecas disponíveis,
        no tipo de modelo solicitado e na linguagem de programação.
        """
        # Verificar se temos bibliotecas de ML disponíveis
        try:
            import torch
            import transformers
            from transformers import AutoModel, AutoTokenizer
            HAVE_TRANSFORMERS = True
            logger.info("Biblioteca Transformers disponível")
        except ImportError:
            HAVE_TRANSFORMERS = False
            logger.warning("Biblioteca Transformers não disponível")

        try:
            import sentence_transformers
            from sentence_transformers import SentenceTransformer
            HAVE_SENTENCE_TRANSFORMERS = True
            logger.info("Biblioteca SentenceTransformers disponível")
        except ImportError:
            HAVE_SENTENCE_TRANSFORMERS = False
            logger.warning("Biblioteca SentenceTransformers não disponível")

        # Determinar quais modelos carregar com base no tipo e linguagem
        primary_model_id = self._get_model_id_for_type_and_language(primary=True)
        secondary_model_id = self._get_model_id_for_type_and_language(primary=False)

        logger.info(f"Tentando carregar modelo primário: {primary_model_id}")
        logger.info(f"Tentando carregar modelo secundário: {secondary_model_id}")

        # Se temos um caminho específico para o modelo, usar ele como primário
        if self.model_path and os.path.exists(self.model_path):
            try:
                if HAVE_SENTENCE_TRANSFORMERS:
                    self.primary_model = SentenceTransformer(self.model_path)
                    logger.info(f"Carregado modelo primário SentenceTransformer de {self.model_path}")
                elif HAVE_TRANSFORMERS:
                    self.primary_tokenizer = AutoTokenizer.from_pretrained(self.model_path)
                    self.primary_model = AutoModel.from_pretrained(self.model_path)
                    logger.info(f"Carregado modelo primário Transformers de {self.model_path}")
            except Exception as e:
                logger.error(f"Erro ao carregar modelo de {self.model_path}: {e}")

        # Se não conseguimos carregar o modelo primário do caminho específico, tentar carregar do ID
        if self.primary_model is None and primary_model_id:
            # Tentar carregar com SentenceTransformers primeiro (mais fácil de usar)
            if HAVE_SENTENCE_TRANSFORMERS:
                try:
                    # Usar cache local para evitar download repetido
                    cache_folder = os.path.join(os.path.expanduser("~"), ".cache", "sentence_transformers")
                    os.makedirs(cache_folder, exist_ok=True)

                    self.primary_model = SentenceTransformer(
                        primary_model_id,
                        cache_folder=cache_folder
                    )
                    logger.info(f"Carregado modelo primário SentenceTransformer: {primary_model_id}")
                except Exception as e:
                    logger.warning(f"Erro ao carregar modelo SentenceTransformer {primary_model_id}: {e}")

            # Se falhar com SentenceTransformers, tentar com Transformers
            if self.primary_model is None and HAVE_TRANSFORMERS:
                try:
                    self.primary_tokenizer = AutoTokenizer.from_pretrained(primary_model_id)
                    self.primary_model = AutoModel.from_pretrained(primary_model_id)
                    logger.info(f"Carregado modelo primário Transformers: {primary_model_id}")
                except Exception as e:
                    logger.warning(f"Erro ao carregar modelo Transformers {primary_model_id}: {e}")

        # Carregar modelo secundário se o primário foi carregado com sucesso
        if self.primary_model is not None and secondary_model_id:
            if HAVE_SENTENCE_TRANSFORMERS:
                try:
                    # Usar cache local para evitar download repetido
                    cache_folder = os.path.join(os.path.expanduser("~"), ".cache", "sentence_transformers")
                    os.makedirs(cache_folder, exist_ok=True)

                    self.secondary_model = SentenceTransformer(
                        secondary_model_id,
                        cache_folder=cache_folder
                    )
                    logger.info(f"Carregado modelo secundário SentenceTransformer: {secondary_model_id}")
                except Exception as e:
                    logger.warning(f"Erro ao carregar modelo secundário SentenceTransformer {secondary_model_id}: {e}")

            # Se falhar com SentenceTransformers, tentar com Transformers
            if self.secondary_model is None and HAVE_TRANSFORMERS:
                try:
                    self.secondary_tokenizer = AutoTokenizer.from_pretrained(secondary_model_id)
                    self.secondary_model = AutoModel.from_pretrained(secondary_model_id)
                    logger.info(f"Carregado modelo secundário Transformers: {secondary_model_id}")
                except Exception as e:
                    logger.warning(f"Erro ao carregar modelo secundário Transformers {secondary_model_id}: {e}")

        # Verificar se conseguimos carregar pelo menos um modelo
        if self.primary_model is None:
            logger.warning("Nenhum modelo de embedding pôde ser carregado. Usando simulação.")

    def _get_model_id_for_type_and_language(self, primary=True):
        """
        Determina o ID do modelo a ser carregado com base no tipo e linguagem.

        Args:
            primary: Se é para o modelo primário (True) ou secundário (False)

        Returns:
            ID do modelo a ser carregado
        """
        # Se temos uma linguagem específica e ela está nos modelos suportados
        if self.language and self.language.lower() in self.AUGMENT_MODELS:
            if primary:
                return self.AUGMENT_MODELS[self.language.lower()]
            else:
                # Para o modelo secundário, usar o modelo de código genérico
                return self.AUGMENT_MODELS['code_secondary']

        # Caso contrário, usar o modelo baseado no tipo
        if self.model_type == 'code':
            return self.AUGMENT_MODELS['code_primary'] if primary else self.AUGMENT_MODELS['code_secondary']
        elif self.model_type == 'text':
            return self.AUGMENT_MODELS['text_primary'] if primary else self.AUGMENT_MODELS['text_secondary']
        elif self.model_type == 'multilingual':
            return self.AUGMENT_MODELS['multilingual']
        else:
            # Tipo desconhecido, usar código como padrão
            return self.AUGMENT_MODELS['code_primary'] if primary else self.AUGMENT_MODELS['code_secondary']

    def embed_query(self, query):
        """
        Converte uma consulta em linguagem natural em um embedding.

        Args:
            query: Consulta em linguagem natural

        Returns:
            Vetor de embedding
        """
        # Verificar cache primeiro
        if self.cache:
            cache_key = f"query_embed_{self._get_cache_key(query)}"
            cached_embedding = self.cache.get(cache_key)
            if cached_embedding is not None:
                self.stats['cache_hits'] += 1
                return cached_embedding
            self.stats['cache_misses'] += 1

        # Pré-processamento da consulta
        processed_query = self._preprocess_query(query)

        # Geração de embedding
        embedding = None

        # Tentar gerar com o modelo primário
        if self.primary_model is not None:
            try:
                import torch

                # Verificar tipo de modelo
                if 'SentenceTransformer' in str(type(self.primary_model)):
                    # Usar SentenceTransformers
                    embedding = self.primary_model.encode(
                        processed_query,
                        convert_to_numpy=True,
                        normalize_embeddings=True
                    )
                    self.stats['primary_model_uses'] += 1
                elif hasattr(self.primary_model, 'forward') and self.primary_tokenizer:
                    # Usar Transformers
                    inputs = self.primary_tokenizer(
                        processed_query,
                        return_tensors="pt",
                        padding=True,
                        truncation=True,
                        max_length=512
                    )

                    with torch.no_grad():
                        outputs = self.primary_model(**inputs)

                    # Usar a representação [CLS] para consultas
                    embedding = outputs.last_hidden_state[:, 0, :].numpy().flatten()

                    # Normalizar
                    embedding = embedding / np.linalg.norm(embedding)
                    self.stats['primary_model_uses'] += 1
            except Exception as e:
                logger.warning(f"Erro ao gerar embedding para consulta com modelo primário: {e}")

        # Tentar com o modelo secundário se o primário falhou
        if embedding is None and self.secondary_model is not None:
            try:
                import torch

                # Verificar tipo de modelo
                if 'SentenceTransformer' in str(type(self.secondary_model)):
                    # Usar SentenceTransformers
                    embedding = self.secondary_model.encode(
                        processed_query,
                        convert_to_numpy=True,
                        normalize_embeddings=True
                    )
                    self.stats['secondary_model_uses'] = self.stats.get('secondary_model_uses', 0) + 1
                    logger.info(f"Embedding gerado com modelo secundário SentenceTransformer")
                elif hasattr(self.secondary_model, 'forward') and self.secondary_tokenizer:
                    # Usar Transformers
                    inputs = self.secondary_tokenizer(
                        processed_query,
                        return_tensors="pt",
                        padding=True,
                        truncation=True,
                        max_length=512
                    )

                    with torch.no_grad():
                        outputs = self.secondary_model(**inputs)

                    # Usar a representação [CLS] para consultas
                    embedding = outputs.last_hidden_state[:, 0, :].numpy().flatten()

                    # Normalizar
                    embedding = embedding / np.linalg.norm(embedding)
                    self.stats['secondary_model_uses'] = self.stats.get('secondary_model_uses', 0) + 1
                    logger.info(f"Embedding gerado com modelo secundário Transformers")
            except Exception as e:
                logger.warning(f"Erro ao gerar embedding para consulta com modelo secundário: {e}")

        # Se ambos os modelos falharam, usar embedding de fallback
        if embedding is None:
            logger.warning(f"Usando sistema de fallback para consulta: {query[:30]}...")
            embedding = self._simulate_embedding(processed_query, is_code=False)
            self.stats['fallback_uses'] += 1

        # Garantir dimensão correta
        if embedding.shape[0] != self.embedding_dim:
            if embedding.shape[0] > self.embedding_dim:
                embedding = embedding[:self.embedding_dim]
            else:
                padding = np.zeros(self.embedding_dim - embedding.shape[0])
                embedding = np.concatenate([embedding, padding])

            # Renormalizar após ajuste de dimensão
            embedding = embedding / np.linalg.norm(embedding)

        # Salvar no cache
        if self.cache:
            self.cache.set(cache_key, embedding)

        return embedding

    def embed_code(self, code):
        """
        Converte um snippet de código em um embedding.

        Args:
            code: Snippet de código

        Returns:
            Vetor de embedding
        """
        # Verificar cache primeiro
        if self.cache:
            cache_key = f"code_embed_{self._get_cache_key(code)}"
            cached_embedding = self.cache.get(cache_key)
            if cached_embedding is not None:
                self.stats['cache_hits'] += 1
                return cached_embedding
            self.stats['cache_misses'] += 1

        # Detectar linguagem
        language = self._detect_language(code)

        # Pré-processamento específico para a linguagem
        processed_code = self._preprocess_code(code, language)

        # Geração de embedding
        embedding = None

        # Tentar gerar com o modelo primário
        if self.primary_model is not None:
            try:
                import torch

                # Verificar tipo de modelo
                if 'SentenceTransformer' in str(type(self.primary_model)):
                    # Usar SentenceTransformers
                    embedding = self.primary_model.encode(
                        processed_code,
                        convert_to_numpy=True,
                        normalize_embeddings=True
                    )
                    self.stats['primary_model_uses'] += 1
                elif hasattr(self.primary_model, 'forward') and self.primary_tokenizer:
                    # Usar Transformers
                    inputs = self.primary_tokenizer(
                        processed_code,
                        return_tensors="pt",
                        padding=True,
                        truncation=True,
                        max_length=512
                    )

                    with torch.no_grad():
                        outputs = self.primary_model(**inputs)

                    # Usar média dos tokens para código
                    embedding = outputs.last_hidden_state.mean(dim=1).numpy().flatten()

                    # Normalizar
                    embedding = embedding / np.linalg.norm(embedding)
                    self.stats['primary_model_uses'] += 1
            except Exception as e:
                logger.warning(f"Erro ao gerar embedding para código com modelo primário: {e}")

        # Tentar com o modelo secundário se o primário falhou
        if embedding is None and self.secondary_model is not None:
            try:
                import torch

                # Verificar tipo de modelo
                if 'SentenceTransformer' in str(type(self.secondary_model)):
                    # Usar SentenceTransformers
                    embedding = self.secondary_model.encode(
                        processed_code,
                        convert_to_numpy=True,
                        normalize_embeddings=True
                    )
                    self.stats['secondary_model_uses'] = self.stats.get('secondary_model_uses', 0) + 1
                    logger.info(f"Embedding de código gerado com modelo secundário SentenceTransformer")
                elif hasattr(self.secondary_model, 'forward') and self.secondary_tokenizer:
                    # Usar Transformers
                    inputs = self.secondary_tokenizer(
                        processed_code,
                        return_tensors="pt",
                        padding=True,
                        truncation=True,
                        max_length=512
                    )

                    with torch.no_grad():
                        outputs = self.secondary_model(**inputs)

                    # Usar média dos tokens para código
                    embedding = outputs.last_hidden_state.mean(dim=1).numpy().flatten()

                    # Normalizar
                    embedding = embedding / np.linalg.norm(embedding)
                    self.stats['secondary_model_uses'] = self.stats.get('secondary_model_uses', 0) + 1
                    logger.info(f"Embedding de código gerado com modelo secundário Transformers")
            except Exception as e:
                logger.warning(f"Erro ao gerar embedding para código com modelo secundário: {e}")

        # Se ambos os modelos falharam, usar embedding de fallback
        if embedding is None:
            logger.warning(f"Usando sistema de fallback para código")
            embedding = self._simulate_embedding(processed_code, is_code=True, language=language)
            self.stats['fallback_uses'] += 1

        # Garantir dimensão correta
        if embedding.shape[0] != self.embedding_dim:
            if embedding.shape[0] > self.embedding_dim:
                embedding = embedding[:self.embedding_dim]
            else:
                padding = np.zeros(self.embedding_dim - embedding.shape[0])
                embedding = np.concatenate([embedding, padding])

            # Renormalizar após ajuste de dimensão
            embedding = embedding / np.linalg.norm(embedding)

        # Salvar no cache
        if self.cache:
            self.cache.set(cache_key, embedding)

        return embedding

    def _get_cache_key(self, text):
        """
        Gera uma chave de cache para um texto.

        Args:
            text: Texto para gerar a chave

        Returns:
            Chave de cache
        """
        # Normalizar texto para consistência
        normalized_text = text.strip()

        # Gerar hash do texto
        text_hash = hashlib.md5(normalized_text.encode('utf-8')).hexdigest()

        # Formato: modeltype_hash
        return f"{self.model_type}_{text_hash}"

    def _simulate_embedding(self, text, is_code=False, language=None):
        """
        Gera um embedding de fallback quando os modelos principais falham.

        Args:
            text: Texto para gerar embedding
            is_code: Se o texto é código
            language: Linguagem de programação (se for código)

        Returns:
            Embedding de fallback
        """
        # Dividir o texto em tokens
        tokens = text.split()

        # Inicializar embedding com zeros
        embedding = np.zeros(self.embedding_dim)

        # Para cada token, adicionar sua contribuição ao embedding
        for i, token in enumerate(tokens):
            # Usar hash do token para gerar valores consistentes
            token_hash = int(hashlib.md5(token.encode('utf-8')).hexdigest(), 16) % (2**32 - 1)
            np.random.seed(token_hash)

            # Gerar vetor para o token
            token_vec = np.random.randn(self.embedding_dim)

            # Adicionar ao embedding com peso baseado na posição
            # Para código, dar mais peso a tokens importantes como nomes de funções e classes
            if is_code:
                # Identificar tokens importantes em código
                important_tokens = ['def', 'class', 'function', 'import', 'from', 'public', 'private']
                if language == 'python':
                    important_tokens.extend(['self', 'return', 'if', 'for', 'while'])
                elif language in ['javascript', 'typescript']:
                    important_tokens.extend(['const', 'let', 'var', 'return', 'this'])

                # Dar mais peso a tokens importantes
                if token in important_tokens:
                    weight = 2.0 / (1 + i * 0.05)
                else:
                    weight = 1.0 / (1 + i * 0.1)
            else:
                # Para consultas, dar peso decrescente com a posição
                weight = 1.0 / (1 + i * 0.1)

            embedding += token_vec * weight

        # Se não houver tokens, gerar um vetor aleatório
        if not tokens:
            # Garantir que o seed esteja dentro do intervalo válido (0 a 2^32-1)
            seed_value = abs(hash(text)) % (2**32 - 1)
            np.random.seed(seed_value)
            embedding = np.random.randn(self.embedding_dim)

        # Normalizar o vetor
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm

        return embedding

    def _preprocess_query(self, query):
        """
        Pré-processa uma consulta em linguagem natural.

        Args:
            query: Consulta original

        Returns:
            Consulta pré-processada
        """
        # Normalização básica
        return query.strip()

    def _preprocess_code(self, code, language=None):
        """
        Pré-processa um snippet de código.

        Args:
            code: Código original
            language: Linguagem de programação detectada

        Returns:
            Código pré-processado
        """
        # Se a linguagem não foi fornecida, tentar detectá-la
        if not language:
            language = self._detect_language(code)

        # Normalização básica
        lines = code.split('\n')
        processed_lines = []

        # Processamento específico por linguagem
        if language == 'python':
            # Processamento específico para Python
            for line in lines:
                # Remover comentários muito longos
                if '#' in line:
                    comment_pos = line.find('#')
                    if len(line[comment_pos:]) > 50:
                        line = line[:comment_pos] + '# ...'

                # Preservar linha se não estiver vazia após processamento
                if line.strip():
                    processed_lines.append(line)
        else:
            # Processamento genérico para outras linguagens
            for line in lines:
                # Preservar linha se não estiver vazia
                if line.strip():
                    processed_lines.append(line)

        # Juntar linhas processadas
        processed_code = '\n'.join(processed_lines)

        # Normalização adicional
        # Remover espaços em branco extras
        processed_code = '\n'.join(line.rstrip() for line in processed_code.split('\n'))

        # Remover linhas em branco consecutivas
        while '\n\n\n' in processed_code:
            processed_code = processed_code.replace('\n\n\n', '\n\n')

        return processed_code

    def _detect_language(self, code):
        """
        Detecta a linguagem de programação de um snippet de código.

        Args:
            code: Snippet de código

        Returns:
            Linguagem detectada ou None se não for possível detectar
        """
        # Verificar extensão de arquivo se o código contém um caminho
        if '\n' not in code and ('/' in code or '\\' in code):
            # Pode ser um caminho de arquivo
            ext = os.path.splitext(code.strip())[1].lower()
            if ext:
                # Mapear extensão para linguagem
                ext_map = {
                    '.py': 'python',
                    '.js': 'javascript',
                    '.ts': 'typescript',
                    '.jsx': 'javascript',
                    '.tsx': 'typescript',
                    '.java': 'java',
                    '.c': 'c',
                    '.cpp': 'cpp',
                    '.cc': 'cpp',
                    '.h': 'cpp',
                    '.hpp': 'cpp',
                    '.cs': 'csharp',
                    '.go': 'go',
                    '.rb': 'ruby',
                    '.php': 'php',
                    '.rs': 'rust',
                    '.html': 'html',
                    '.htm': 'html',
                    '.css': 'css',
                    '.scss': 'css',
                    '.sql': 'sql',
                    '.kt': 'kotlin',
                    '.swift': 'swift',
                    '.scala': 'scala',
                    '.dart': 'dart',
                    '.r': 'r',
                    '.sh': 'shell',
                    '.bash': 'shell',
                    '.ps1': 'powershell'
                }
                if ext in ext_map:
                    return ext_map[ext]

        # Detecção baseada em padrões no código
        code_lower = code.lower()

        # Python
        if ("def " in code and (":" in code or "import " in code)) or "import numpy" in code_lower or "import pandas" in code_lower:
            return "python"

        # JavaScript/TypeScript
        if "function " in code or "const " in code or "let " in code or "var " in code:
            if "interface " in code or "type " in code or ": " in code or ": any" in code_lower:
                return "typescript"
            return "javascript"

        # Java
        if ("public class " in code or "private class " in code) and ".java" in code_lower:
            return "java"

        # C/C++
        if "#include" in code:
            if "std::" in code or "template<" in code or "namespace " in code:
                return "cpp"
            return "c"

        # C#
        if "using System" in code or "namespace " in code and ".cs" in code_lower:
            return "csharp"

        # Go
        if "package " in code and "func " in code:
            return "go"

        # Ruby
        if "def " in code and "end" in code and "require " in code:
            return "ruby"

        # PHP
        if "<?php" in code or "function " in code and "$" in code:
            return "php"

        # Rust
        if "fn " in code and "let mut " in code:
            return "rust"

        # HTML
        if "<!DOCTYPE html>" in code_lower or "<html" in code_lower:
            return "html"

        # CSS
        if "{" in code and "}" in code and (":" in code and ";" in code) and not "function" in code_lower:
            if "#" in code and "." in code and not "def " in code:
                return "css"

        # SQL
        if "SELECT " in code.upper() and "FROM " in code.upper():
            return "sql"

        # Kotlin
        if "fun " in code and "val " in code:
            return "kotlin"

        # Swift
        if "func " in code and "let " in code and "import Swift" in code:
            return "swift"

        # Shell
        if "#!/bin/bash" in code or "#!/bin/sh" in code:
            return "shell"

        # PowerShell
        if "$" in code and "-" in code and "function " in code:
            return "powershell"

        # Fallback para o tipo de modelo configurado
        if self.language:
            return self.language

        return "code"  # Código genérico

    def get_model_info(self):
        """
        Obtém informações sobre o modelo de embedding.

        Returns:
            Informações sobre o modelo
        """
        # Informações básicas
        info = {
            'name': 'Augment Embedding Model',
            'version': '1.0.0',
            'type': self.model_type,
            'language': self.language,
            'embedding_dim': self.embedding_dim
        }

        # Adicionar estatísticas
        info['stats'] = self.stats.copy()

        # Adicionar informações sobre modelos carregados
        info['primary_model'] = {
            'type': type(self.primary_model).__name__ if self.primary_model else None,
            'available': self.primary_model is not None
        }

        return info
        self.embedder = Llama(
            model_path="C:\\Users\\<USER>\\.lmstudio\\models\\nomic-ai\\nomic-embed-text-v2-moe-GGUF\\nomic-embed-text.gguf",
            embedding=True,
            n_ctx=2048
        )