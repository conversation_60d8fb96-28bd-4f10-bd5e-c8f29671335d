"""
Indexação de Código

Este módulo implementa a indexação de código para o motor de contexto,
permitindo a criação e manutenção de índices de código eficientes.
"""

import os
import re
import json
import pickle
import logging
import hashlib
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Set, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# Configurar logger
logger = logging.getLogger("augment.indexing")

class CodeChunk:
    """
    Representa um chunk de código com metadados.
    """

    def __init__(self, content: str, file_path: str, start_line: int, end_line: int,
                 language: Optional[str] = None, symbols: Optional[List[str]] = None):
        """
        Inicializa um chunk de código.

        Args:
            content: Conteúdo do chunk
            file_path: Caminho do arquivo
            start_line: Linha inicial (1-based)
            end_line: Linha final (1-based)
            language: Linguagem de programação
            symbols: Símbolos importantes no chunk (funções, classes, etc.)
        """
        self.content = content
        self.file_path = file_path
        self.start_line = start_line
        self.end_line = end_line
        self.language = language
        self.symbols = symbols or []
        self.embedding = None
        self.metadata = {
            'file_path': file_path,
            'start_line': start_line,
            'end_line': end_line,
            'language': language,
            'symbols': symbols or [],
            'chunk_hash': self._compute_hash()
        }

    def _compute_hash(self) -> str:
        """
        Computa um hash para o chunk.

        Returns:
            Hash do chunk
        """
        content_hash = hashlib.md5(self.content.encode('utf-8')).hexdigest()
        return f"{self.file_path}:{self.start_line}-{self.end_line}:{content_hash[:8]}"

    def to_dict(self) -> Dict[str, Any]:
        """
        Converte o chunk para um dicionário.

        Returns:
            Dicionário com os dados do chunk
        """
        return {
            'content': self.content,
            'file_path': self.file_path,
            'start_line': self.start_line,
            'end_line': self.end_line,
            'language': self.language,
            'symbols': self.symbols,
            'metadata': self.metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CodeChunk':
        """
        Cria um chunk a partir de um dicionário.

        Args:
            data: Dicionário com os dados do chunk

        Returns:
            Chunk de código
        """
        chunk = cls(
            content=data['content'],
            file_path=data['file_path'],
            start_line=data['start_line'],
            end_line=data['end_line'],
            language=data['language'],
            symbols=data['symbols']
        )

        # Restaurar metadados adicionais se existirem
        if 'metadata' in data:
            for key, value in data['metadata'].items():
                if key not in chunk.metadata:
                    chunk.metadata[key] = value

        return chunk

class CodeIndex:
    """
    Índice de código que armazena embeddings e metadados para busca eficiente.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Inicializa o índice de código.

        Args:
            config: Configuração do índice
        """
        self.config = config or {}
        self.embedding_dim = self.config.get('embedding_dim', 768)
        self.persist_dir = self.config.get('persist_dir', './index_data')
        self.use_faiss = self.config.get('use_faiss', True)
        self.is_partitioned = self.config.get('is_partitioned', False)
        self.partitions = self.config.get('partitions', [])

        # Dados do índice
        self.documents = []  # Lista de CodeChunk
        self.embeddings = np.zeros((0, self.embedding_dim))  # Matriz de embeddings
        self.metadata = []  # Lista de metadados
        self.document_lookup = {}  # Mapeamento de hash para índice

        # Índice FAISS para busca eficiente
        self.faiss_index = None

        # Inicializar índice FAISS se disponível
        if self.use_faiss:
            self._init_faiss()

    def _init_faiss(self):
        """
        Inicializa o índice FAISS.
        """
        try:
            import faiss
            self.faiss_index = faiss.IndexFlatIP(self.embedding_dim)
            logger.info("Índice FAISS inicializado com sucesso")
        except ImportError:
            logger.warning("Biblioteca FAISS não disponível. Usando busca de similaridade padrão.")
            self.use_faiss = False

    def add(self, chunk: CodeChunk, embedding: np.ndarray):
        """
        Adiciona um chunk de código ao índice.

        Args:
            chunk: Chunk de código
            embedding: Embedding do chunk
        """
        # Verificar se o chunk já existe
        chunk_hash = chunk.metadata['chunk_hash']
        if chunk_hash in self.document_lookup:
            # Atualizar embedding e metadados
            idx = self.document_lookup[chunk_hash]
            self.documents[idx] = chunk
            self.embeddings[idx] = embedding
            self.metadata[idx] = chunk.metadata
            return

        # Adicionar novo chunk
        self.documents.append(chunk)

        # Adicionar embedding
        if len(self.embeddings) == 0:
            self.embeddings = np.array([embedding])
        else:
            self.embeddings = np.vstack([self.embeddings, embedding])

        # Adicionar metadados
        self.metadata.append(chunk.metadata)

        # Atualizar lookup
        self.document_lookup[chunk_hash] = len(self.documents) - 1

        # Atualizar índice FAISS
        if self.use_faiss and self.faiss_index is not None:
            import faiss
            self.faiss_index.add(np.array([embedding], dtype=np.float32))

    def search(self, query_embedding: np.ndarray, top_k: int = 5,
               filter_criteria: Optional[Dict[str, Any]] = None) -> List[Tuple[int, float, Dict[str, Any]]]:
        """
        Busca chunks de código similares a um embedding de consulta.

        Args:
            query_embedding: Embedding da consulta
            top_k: Número máximo de resultados
            filter_criteria: Critérios de filtro para os resultados

        Returns:
            Lista de tuplas (índice, similaridade, metadados)
        """
        if self.is_partitioned:
            return self._search_partitioned(query_embedding, top_k, filter_criteria)

        if len(self.documents) == 0:
            return []

        # Determinar o número de resultados a buscar inicialmente
        # Se temos filtros, precisamos buscar mais resultados para compensar os que serão filtrados
        initial_top_k = top_k
        if filter_criteria:
            initial_top_k = min(top_k * 5, len(self.documents))  # Buscar 5x mais resultados se temos filtros
        else:
            initial_top_k = min(top_k, len(self.documents))

        # Buscar usando FAISS se disponível
        if self.use_faiss and self.faiss_index is not None:
            try:
                import faiss
                # Normalizar embedding de consulta
                query_embedding_norm = query_embedding / np.linalg.norm(query_embedding)
                query_embedding_norm = np.array([query_embedding_norm], dtype=np.float32)

                # Buscar no índice FAISS
                similarities, indices = self.faiss_index.search(query_embedding_norm, initial_top_k)

                # Converter para o formato de resultado
                results = []
                for i in range(len(indices[0])):
                    idx = indices[0][i]
                    if idx == -1:  # -1 indica resultado inválido
                        continue

                    similarity = similarities[0][i]

                    # Verificar filtros
                    if filter_criteria and not self._matches_filter(self.metadata[idx], filter_criteria):
                        continue

                    results.append((idx, float(similarity), self.metadata[idx]))

                    if len(results) >= top_k:
                        break

                return results
            except Exception as e:
                logger.error(f"Erro ao buscar com FAISS: {e}")
                # Fallback para busca por força bruta

        # Busca por força bruta
        # Otimização: pré-filtrar metadados se possível
        if filter_criteria:
            # Criar lista de índices que passam pelo filtro
            filtered_indices = []
            for i, meta in enumerate(self.metadata):
                if self._matches_filter(meta, filter_criteria):
                    filtered_indices.append(i)

            if filtered_indices:
                # Normalizar embedding de consulta
                query_embedding_norm = query_embedding / np.linalg.norm(query_embedding)

                # Calcular similaridades apenas para os documentos filtrados
                filtered_embeddings = self.embeddings[filtered_indices]
                similarities = np.dot(filtered_embeddings, query_embedding_norm)

                # Obter índices ordenados
                sorted_indices = np.argsort(similarities)[::-1]

                # Adicionar resultados
                results = []
                for i in sorted_indices[:top_k]:
                    idx = filtered_indices[i]
                    similarity = similarities[i]
                    results.append((idx, float(similarity), self.metadata[idx]))

                return results

        # Busca padrão sem filtro ou se a otimização de filtro falhou
        # Normalizar embedding de consulta
        query_embedding_norm = query_embedding / np.linalg.norm(query_embedding)

        # Calcular similaridade com todos os embeddings
        similarities = np.dot(self.embeddings, query_embedding_norm)

        # Usar argpartition para encontrar os top_k mais eficientemente
        # (mais rápido que argsort para grandes arrays quando só precisamos dos top_k)
        if len(similarities) > 1000:  # Usar apenas para arrays grandes
            top_indices = np.argpartition(-similarities, min(initial_top_k, len(similarities)-1))[:initial_top_k]
            # Ordenar apenas os top_k por similaridade
            top_indices = top_indices[np.argsort(-similarities[top_indices])]
        else:
            # Para arrays pequenos, argsort é suficientemente rápido
            top_indices = np.argsort(similarities)[::-1][:initial_top_k]

        # Aplicar filtros e retornar resultados
        results = []
        for idx in top_indices:
            similarity = similarities[idx]

            # Verificar filtros
            if filter_criteria and not self._matches_filter(self.metadata[idx], filter_criteria):
                continue

            results.append((int(idx), float(similarity), self.metadata[idx]))

            if len(results) >= top_k:
                break

        return results

    def _search_partitioned(self, query_embedding: np.ndarray, top_k: int = 5,
                           filter_criteria: Optional[Dict[str, Any]] = None) -> List[Tuple[int, float, Dict[str, Any]]]:
        """
        Busca em índices particionados.

        Args:
            query_embedding: Embedding da consulta
            top_k: Número máximo de resultados
            filter_criteria: Critérios de filtro para os resultados

        Returns:
            Lista de tuplas (índice, similaridade, metadados)
        """
        all_results = []

        # Buscar em cada partição
        for partition_info in self.partitions:
            partition_path = partition_info['path']

            # Carregar índice da partição
            partition_index = CodeIndex(config={
                'embedding_dim': self.embedding_dim,
                'persist_dir': partition_path,
                'use_faiss': self.use_faiss
            })

            try:
                partition_index.load()

                # Buscar na partição
                partition_results = partition_index.search(
                    query_embedding,
                    top_k=top_k,
                    filter_criteria=filter_criteria
                )

                # Adicionar resultados
                all_results.extend(partition_results)
            except Exception as e:
                logger.error(f"Erro ao buscar na partição {partition_path}: {e}")

        # Ordenar todos os resultados por similaridade
        all_results.sort(key=lambda x: x[1], reverse=True)

        # Retornar os top_k resultados
        return all_results[:top_k]

    def _matches_filter(self, metadata: Dict[str, Any], filter_criteria: Dict[str, Any]) -> bool:
        """
        Verifica se um documento corresponde aos critérios de filtro.

        Args:
            metadata: Metadados do documento
            filter_criteria: Critérios de filtro

        Returns:
            True se corresponde, False caso contrário
        """
        for key, value in filter_criteria.items():
            if key not in metadata:
                return False

            if isinstance(value, list):
                # Se o valor é uma lista, verificar se pelo menos um valor corresponde
                if metadata[key] not in value:
                    return False
            elif isinstance(value, dict):
                # Se o valor é um dicionário, verificar operadores especiais
                for op, op_value in value.items():
                    if op == 'eq' and metadata[key] != op_value:
                        return False
                    elif op == 'ne' and metadata[key] == op_value:
                        return False
                    elif op == 'gt' and metadata[key] <= op_value:
                        return False
                    elif op == 'lt' and metadata[key] >= op_value:
                        return False
                    elif op == 'gte' and metadata[key] < op_value:
                        return False
                    elif op == 'lte' and metadata[key] > op_value:
                        return False
                    elif op == 'in' and metadata[key] not in op_value:
                        return False
                    elif op == 'nin' and metadata[key] in op_value:
                        return False
            else:
                # Comparação direta
                if metadata[key] != value:
                    return False

        return True

    def get(self, idx: int) -> Optional[CodeChunk]:
        """
        Obtém um chunk de código pelo índice.

        Args:
            idx: Índice do chunk

        Returns:
            Chunk de código ou None se não encontrado
        """
        if idx < 0 or idx >= len(self.documents):
            return None

        return self.documents[idx]

    def size(self) -> int:
        """
        Obtém o número de documentos no índice.

        Returns:
            Número de documentos
        """
        return len(self.documents)

    def save(self, path: Optional[str] = None):
        """
        Salva o índice em disco.

        Args:
            path: Caminho para salvar o índice (opcional)
        """
        save_path = Path(path or self.persist_dir)
        save_path.mkdir(exist_ok=True, parents=True)

        # Salvar documentos
        with open(save_path / 'documents.pkl', 'wb') as f:
            pickle.dump([doc.to_dict() for doc in self.documents], f)

        # Salvar embeddings
        np.save(save_path / 'embeddings.npy', self.embeddings)

        # Salvar metadados
        with open(save_path / 'metadata.json', 'w') as f:
            json.dump({
                'size': len(self.documents),
                'embedding_dim': self.embedding_dim,
                'use_faiss': self.use_faiss,
                'is_partitioned': self.is_partitioned,
                'partitions': self.partitions
            }, f)

        logger.info(f"Índice salvo em {save_path} com {len(self.documents)} documentos")

    def load(self, path: Optional[str] = None):
        """
        Carrega o índice do disco.

        Args:
            path: Caminho para carregar o índice (opcional)
        """
        load_path = Path(path or self.persist_dir)

        if not load_path.exists():
            raise FileNotFoundError(f"Diretório do índice não encontrado: {load_path}")

        # Carregar metadados
        try:
            with open(load_path / 'metadata.json', 'r') as f:
                metadata = json.load(f)

            self.embedding_dim = metadata.get('embedding_dim', self.embedding_dim)
            self.use_faiss = metadata.get('use_faiss', self.use_faiss)
            self.is_partitioned = metadata.get('is_partitioned', self.is_partitioned)
            self.partitions = metadata.get('partitions', self.partitions)
        except Exception as e:
            logger.error(f"Erro ao carregar metadados do índice: {e}")
            raise

        # Carregar documentos
        try:
            with open(load_path / 'documents.pkl', 'rb') as f:
                documents_data = pickle.load(f)

            self.documents = [CodeChunk.from_dict(data) for data in documents_data]
            self.metadata = [doc.metadata for doc in self.documents]
            self.document_lookup = {doc.metadata['chunk_hash']: i for i, doc in enumerate(self.documents)}
        except Exception as e:
            logger.error(f"Erro ao carregar documentos do índice: {e}")
            raise

        # Carregar embeddings
        try:
            self.embeddings = np.load(load_path / 'embeddings.npy')
        except Exception as e:
            logger.error(f"Erro ao carregar embeddings do índice: {e}")
            raise

        # Inicializar índice FAISS
        if self.use_faiss:
            self._init_faiss()

            # Adicionar embeddings ao índice FAISS
            if self.faiss_index is not None and len(self.embeddings) > 0:
                import faiss
                self.faiss_index.add(self.embeddings.astype(np.float32))

        logger.info(f"Índice carregado de {load_path} com {len(self.documents)} documentos")

    def merge(self, other_index: 'CodeIndex'):
        """
        Mescla outro índice com este.

        Args:
            other_index: Outro índice para mesclar
        """
        # Verificar compatibilidade
        if self.embedding_dim != other_index.embedding_dim:
            raise ValueError(f"Dimensões de embedding incompatíveis: {self.embedding_dim} vs {other_index.embedding_dim}")

        # Adicionar documentos do outro índice
        for i, doc in enumerate(other_index.documents):
            embedding = other_index.embeddings[i]
            self.add(doc, embedding)

        logger.info(f"Índices mesclados. Novo tamanho: {len(self.documents)} documentos")

class CodeIndexer:
    """
    Indexador de código que processa arquivos e cria índices.
    """

    def __init__(self, embedding_model, config: Optional[Dict[str, Any]] = None):
        """
        Inicializa o indexador de código.

        Args:
            embedding_model: Modelo de embedding para converter código em vetores
            config: Configuração do indexador
        """
        self.embedding_model = embedding_model
        self.config = config or {}

        # Configurações
        self.supported_extensions = self.config.get('supported_extensions', [
            '.py', '.js', '.ts', '.java', '.c', '.cpp', '.cs', '.go', '.rb',
            '.php', '.swift', '.kt', '.rs', '.scala', '.html', '.css', '.jsx', '.tsx'
        ])
        self.max_file_size = self.config.get('max_file_size', 1024 * 1024)  # 1MB
        self.max_workers = self.config.get('max_workers', 4)
        self.chunk_size = self.config.get('chunk_size', 1000)
        self.chunk_overlap = self.config.get('chunk_overlap', 200)
        self.cache_dir = self.config.get('cache_dir', './indexer_cache')

        # Criar diretório de cache
        os.makedirs(self.cache_dir, exist_ok=True)

    def should_index_file(self, file_path: str) -> bool:
        """
        Verifica se um arquivo deve ser indexado.

        Args:
            file_path: Caminho do arquivo

        Returns:
            True se o arquivo deve ser indexado, False caso contrário
        """
        # Verificar extensão
        _, ext = os.path.splitext(file_path.lower())
        if ext not in self.supported_extensions:
            return False

        # Verificar tamanho
        try:
            size = os.path.getsize(file_path)
            if size > self.max_file_size:
                logger.warning(f"Arquivo muito grande para indexação: {file_path} ({size} bytes)")
                return False

            if size == 0:
                logger.warning(f"Arquivo vazio: {file_path}")
                return False
        except (OSError, IOError) as e:
            logger.error(f"Erro ao verificar tamanho do arquivo {file_path}: {e}")
            return False

        # Verificar se é um arquivo binário
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                try:
                    f.read(1024)
                except UnicodeDecodeError:
                    logger.warning(f"Arquivo binário: {file_path}")
                    return False
        except (OSError, IOError) as e:
            logger.error(f"Erro ao verificar se o arquivo é binário {file_path}: {e}")
            return False

        return True

    def index_codebase(self, codebase_path: str) -> CodeIndex:
        """
        Indexa um codebase completo.

        Args:
            codebase_path: Caminho para o codebase

        Returns:
            Índice de código
        """
        logger.info(f"Indexando codebase: {codebase_path}")

        # Coletar arquivos a serem indexados
        files_to_index = []
        for root, _, files in os.walk(codebase_path):
            for file in files:
                file_path = os.path.join(root, file)
                if self.should_index_file(file_path):
                    files_to_index.append(file_path)

        logger.info(f"Encontrados {len(files_to_index)} arquivos para indexar")

        # Indexar arquivos
        return self.index_files(files_to_index)

    def index_files(self, file_paths: List[str]) -> CodeIndex:
        """
        Indexa uma lista de arquivos.

        Args:
            file_paths: Lista de caminhos de arquivos

        Returns:
            Índice de código
        """
        # Criar índice
        index = CodeIndex(config={
            'embedding_dim': self.embedding_model.embedding_dim,
            'use_faiss': True
        })

        # Processar arquivos em paralelo
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submeter tarefas
            future_to_file = {
                executor.submit(self._process_file, file_path): file_path
                for file_path in file_paths
            }

            # Processar resultados
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    chunks = future.result()
                    if chunks:
                        for chunk in chunks:
                            # Gerar embedding para o chunk
                            embedding = self.embedding_model.embed_code(chunk.content)

                            # Adicionar ao índice
                            index.add(chunk, embedding)
                except Exception as e:
                    logger.error(f"Erro ao processar arquivo {file_path}: {e}")

        logger.info(f"Indexação concluída. {index.size()} chunks indexados.")

        return index

    def _process_file(self, file_path: str) -> List[CodeChunk]:
        """
        Processa um arquivo para indexação.

        Args:
            file_path: Caminho do arquivo

        Returns:
            Lista de chunks de código
        """
        logger.debug(f"Processando arquivo: {file_path}")

        try:
            # Ler conteúdo do arquivo
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Detectar linguagem
            _, ext = os.path.splitext(file_path.lower())
            language = None
            for lang, exts in {
                'python': ['.py'],
                'javascript': ['.js', '.jsx'],
                'typescript': ['.ts', '.tsx'],
                'java': ['.java'],
                'c': ['.c', '.h'],
                'cpp': ['.cpp', '.hpp'],
                'csharp': ['.cs'],
                'go': ['.go'],
                'ruby': ['.rb'],
                'php': ['.php'],
                'swift': ['.swift'],
                'kotlin': ['.kt'],
                'rust': ['.rs'],
                'scala': ['.scala'],
                'html': ['.html', '.htm'],
                'css': ['.css', '.scss', '.sass'],
                'sql': ['.sql']
            }.items():
                if ext in exts:
                    language = lang
                    break

            # Dividir em chunks
            return self._split_into_chunks(content, file_path, language)
        except Exception as e:
            logger.error(f"Erro ao processar arquivo {file_path}: {e}")
            return []

    def _split_into_chunks(self, content: str, file_path: str, language: Optional[str] = None) -> List[CodeChunk]:
        """
        Divide o conteúdo de um arquivo em chunks.

        Args:
            content: Conteúdo do arquivo
            file_path: Caminho do arquivo
            language: Linguagem de programação

        Returns:
            Lista de chunks de código
        """
        # Dividir em linhas
        lines = content.split('\n')

        # Extrair símbolos importantes
        symbols = self._extract_symbols(content, language)

        # Dividir em chunks
        chunks = []

        # Estratégia 1: Dividir por função/classe
        if language in ['python', 'javascript', 'typescript', 'java', 'csharp']:
            symbol_chunks = self._split_by_symbols(lines, language)
            if symbol_chunks:
                for start, end, chunk_symbols in symbol_chunks:
                    chunk_content = '\n'.join(lines[start:end+1])
                    chunks.append(CodeChunk(
                        content=chunk_content,
                        file_path=file_path,
                        start_line=start + 1,  # 1-based
                        end_line=end + 1,      # 1-based
                        language=language,
                        symbols=chunk_symbols
                    ))
                return chunks

        # Estratégia 2: Dividir por tamanho fixo com sobreposição
        i = 0
        while i < len(lines):
            # Determinar fim do chunk
            end = min(i + self.chunk_size, len(lines))

            # Criar chunk
            chunk_content = '\n'.join(lines[i:end])
            chunks.append(CodeChunk(
                content=chunk_content,
                file_path=file_path,
                start_line=i + 1,      # 1-based
                end_line=end,          # 1-based
                language=language,
                symbols=symbols
            ))

            # Avançar com sobreposição
            i += self.chunk_size - self.chunk_overlap

        return chunks

    def _split_by_symbols(self, lines: List[str], language: str) -> List[Tuple[int, int, List[str]]]:
        """
        Divide o código por símbolos (funções, classes, etc.).

        Args:
            lines: Linhas do arquivo
            language: Linguagem de programação

        Returns:
            Lista de tuplas (linha_inicial, linha_final, símbolos)
        """
        chunks = []

        if language == 'python':
            # Padrões para Python
            class_pattern = re.compile(r'^class\s+(\w+)')
            func_pattern = re.compile(r'^def\s+(\w+)')

            current_start = 0
            current_symbols = []

            for i, line in enumerate(lines):
                line = line.strip()

                # Detectar início de classe ou função
                class_match = class_pattern.match(line)
                func_match = func_pattern.match(line)

                if class_match or func_match:
                    # Se já temos um chunk em andamento, finalizá-lo
                    if current_symbols:
                        chunks.append((current_start, i - 1, current_symbols))

                    # Iniciar novo chunk
                    current_start = i
                    if class_match:
                        current_symbols = [f"class:{class_match.group(1)}"]
                    else:
                        current_symbols = [f"function:{func_match.group(1)}"]

            # Adicionar último chunk
            if current_symbols:
                chunks.append((current_start, len(lines) - 1, current_symbols))

        elif language in ['javascript', 'typescript']:
            # Padrões para JavaScript/TypeScript
            class_pattern = re.compile(r'^class\s+(\w+)')
            func_pattern = re.compile(r'^(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s*)?\([^)]*\)\s*=>|(\w+)\s*:\s*(?:async\s*)?\([^)]*\)\s*=>)')

            current_start = 0
            current_symbols = []

            for i, line in enumerate(lines):
                line = line.strip()

                # Detectar início de classe ou função
                class_match = class_pattern.match(line)
                func_match = func_pattern.match(line)

                if class_match or func_match:
                    # Se já temos um chunk em andamento, finalizá-lo
                    if current_symbols:
                        chunks.append((current_start, i - 1, current_symbols))

                    # Iniciar novo chunk
                    current_start = i
                    if class_match:
                        current_symbols = [f"class:{class_match.group(1)}"]
                    else:
                        func_name = next(filter(None, func_match.groups()), "anonymous")
                        current_symbols = [f"function:{func_name}"]

            # Adicionar último chunk
            if current_symbols:
                chunks.append((current_start, len(lines) - 1, current_symbols))

        return chunks

    def _extract_symbols(self, content: str, language: Optional[str] = None) -> List[str]:
        """
        Extrai símbolos importantes do código.

        Args:
            content: Conteúdo do arquivo
            language: Linguagem de programação

        Returns:
            Lista de símbolos
        """
        symbols = []

        if language == 'python':
            # Extrair classes
            class_pattern = re.compile(r'class\s+(\w+)')
            for match in class_pattern.finditer(content):
                symbols.append(f"class:{match.group(1)}")

            # Extrair funções
            func_pattern = re.compile(r'def\s+(\w+)')
            for match in func_pattern.finditer(content):
                symbols.append(f"function:{match.group(1)}")

        elif language in ['javascript', 'typescript']:
            # Extrair classes
            class_pattern = re.compile(r'class\s+(\w+)')
            for match in class_pattern.finditer(content):
                symbols.append(f"class:{match.group(1)}")

            # Extrair funções
            func_pattern = re.compile(r'function\s+(\w+)')
            for match in func_pattern.finditer(content):
                symbols.append(f"function:{match.group(1)}")

            # Extrair funções arrow
            arrow_func_pattern = re.compile(r'const\s+(\w+)\s*=\s*(?:async\s*)?\([^)]*\)\s*=>')
            for match in arrow_func_pattern.finditer(content):
                symbols.append(f"function:{match.group(1)}")

        return symbols

    def rebuild_indices(self, force: bool = False):
        """
        Reconstrói todos os índices do zero
        
        Args:
            force: Forçar reconstrução mesmo se índices existirem
        """
        if force:
            logger.info("Reconstruindo índices forçadamente")
            self.documents.clear()
            self.embeddings = np.zeros((0, self.embedding_dim))
            self.metadata.clear()
            self.document_lookup.clear()
            
            if self.use_faiss and self.faiss_index:
                import faiss
                self.faiss_index.reset()
        else:
            logger.info("Recarregando índices existentes")
            self._load_index_data()

    def _matches_filter(self, metadata: Dict[str, Any], filter_criteria: Dict[str, Any]) -> bool:
        """
        Verifica se um documento corresponde aos critérios de filtro.

        Args:
            metadata: Metadados do documento
            filter_criteria: Critérios de filtro

        Returns:
            True se corresponde, False caso contrário
        """
        for key, value in filter_criteria.items():
            if key not in metadata:
                return False

            if isinstance(value, list):
                # Se o valor é uma lista, verificar se pelo menos um valor corresponde
                if metadata[key] not in value:
                    return False
            elif isinstance(value, dict):
                # Se o valor é um dicionário, verificar operadores especiais
                for op, op_value in value.items():
                    if op == 'eq' and metadata[key] != op_value:
                        return False
                    elif op == 'ne' and metadata[key] == op_value:
                        return False
                    elif op == 'gt' and metadata[key] <= op_value:
                        return False
                    elif op == 'lt' and metadata[key] >= op_value:
                        return False
                    elif op == 'gte' and metadata[key] < op_value:
                        return False
                    elif op == 'lte' and metadata[key] > op_value:
                        return False
                    elif op == 'in' and metadata[key] not in op_value:
                        return False
                    elif op == 'nin' and metadata[key] in op_value:
                        return False
            else:
                # Comparação direta
                if metadata[key] != value:
                    return False

        return True

    def get(self, idx: int) -> Optional[CodeChunk]:
        """
        Obtém um chunk de código pelo índice.

        Args:
            idx: Índice do chunk

        Returns:
            Chunk de código ou None se não encontrado
        """
        if idx < 0 or idx >= len(self.documents):
            return None

        return self.documents[idx]

    def size(self) -> int:
        """
        Obtém o número de documentos no índice.

        Returns:
            Número de documentos
        """
        return len(self.documents)

    def save(self, path: Optional[str] = None):
        """
        Salva o índice em disco.

        Args:
            path: Caminho para salvar o índice (opcional)
        """
        save_path = Path(path or self.persist_dir)
        save_path.mkdir(exist_ok=True, parents=True)

        # Salvar documentos
        with open(save_path / 'documents.pkl', 'wb') as f:
            pickle.dump([doc.to_dict() for doc in self.documents], f)

        # Salvar embeddings
        np.save(save_path / 'embeddings.npy', self.embeddings)

        # Salvar metadados
        with open(save_path / 'metadata.json', 'w') as f:
            json.dump({
                'size': len(self.documents),
                'embedding_dim': self.embedding_dim,
                'use_faiss': self.use_faiss,
                'is_partitioned': self.is_partitioned,
                'partitions': self.partitions
            }, f)

        logger.info(f"Índice salvo em {save_path} com {len(self.documents)} documentos")

    def load(self, path: Optional[str] = None):
        """
        Carrega o índice do disco.

        Args:
            path: Caminho para carregar o índice (opcional)
        """
        load_path = Path(path or self.persist_dir)

        if not load_path.exists():
            raise FileNotFoundError(f"Diretório do índice não encontrado: {load_path}")

        # Carregar metadados
        try:
            with open(load_path / 'metadata.json', 'r') as f:
                metadata = json.load(f)

            self.embedding_dim = metadata.get('embedding_dim', self.embedding_dim)
            self.use_faiss = metadata.get('use_faiss', self.use_faiss)
            self.is_partitioned = metadata.get('is_partitioned', self.is_partitioned)
            self.partitions = metadata.get('partitions', self.partitions)
        except Exception as e:
            logger.error(f"Erro ao carregar metadados do índice: {e}")
            raise

        # Carregar documentos
        try:
            with open(load_path / 'documents.pkl', 'rb') as f:
                documents_data = pickle.load(f)

            self.documents = [CodeChunk.from_dict(data) for data in documents_data]
            self.metadata = [doc.metadata for doc in self.documents]
            self.document_lookup = {doc.metadata['chunk_hash']: i for i, doc in enumerate(self.documents)}
        except Exception as e:
            logger.error(f"Erro ao carregar documentos do índice: {e}")
            raise

        # Carregar embeddings
        try:
            self.embeddings = np.load(load_path / 'embeddings.npy')
        except Exception as e:
            logger.error(f"Erro ao carregar embeddings do índice: {e}")
            raise

        # Inicializar índice FAISS
        if self.use_faiss:
            self._init_faiss()

            # Adicionar embeddings ao índice FAISS
            if self.faiss_index is not None and len(self.embeddings) > 0:
                import faiss
                self.faiss_index.add(self.embeddings.astype(np.float32))

        logger.info(f"Índice carregado de {load_path} com {len(self.documents)} documentos")

    def merge(self, other_index: 'CodeIndex'):
        """
        Mescla outro índice com este.

        Args:
            other_index: Outro índice para mesclar
        """
        # Verificar compatibilidade
        if self.embedding_dim != other_index.embedding_dim:
            raise ValueError(f"Dimensões de embedding incompatíveis: {self.embedding_dim} vs {other_index.embedding_dim}")

        # Adicionar documentos do outro índice
        for i, doc in enumerate(other_index.documents):
            embedding = other_index.embeddings[i]
            self.add(doc, embedding)

        logger.info(f"Índices mesclados. Novo tamanho: {len(self.documents)} documentos")

class CodeIndexer:
    """
    Indexador de código que processa arquivos e cria índices.
    """

    def __init__(self, embedding_model, config: Optional[Dict[str, Any]] = None):
        """
        Inicializa o indexador de código.

        Args:
            embedding_model: Modelo de embedding para converter código em vetores
            config: Configuração do indexador
        """
        self.embedding_model = embedding_model
        self.config = config or {}

        # Configurações
        self.supported_extensions = self.config.get('supported_extensions', [
            '.py', '.js', '.ts', '.java', '.c', '.cpp', '.cs', '.go', '.rb',
            '.php', '.swift', '.kt', '.rs', '.scala', '.html', '.css', '.jsx', '.tsx'
        ])
        self.max_file_size = self.config.get('max_file_size', 1024 * 1024)  # 1MB
        self.max_workers = self.config.get('max_workers', 4)
        self.chunk_size = self.config.get('chunk_size', 1000)
        self.chunk_overlap = self.config.get('chunk_overlap', 200)
        self.cache_dir = self.config.get('cache_dir', './indexer_cache')

        # Criar diretório de cache
        os.makedirs(self.cache_dir, exist_ok=True)

    def should_index_file(self, file_path: str) -> bool:
        """
        Verifica se um arquivo deve ser indexado.

        Args:
            file_path: Caminho do arquivo

        Returns:
            True se o arquivo deve ser indexado, False caso contrário
        """
        # Verificar extensão
        _, ext = os.path.splitext(file_path.lower())
        if ext not in self.supported_extensions:
            return False

        # Verificar tamanho
        try:
            size = os.path.getsize(file_path)
            if size > self.max_file_size:
                logger.warning(f"Arquivo muito grande para indexação: {file_path} ({size} bytes)")
                return False

            if size == 0:
                logger.warning(f"Arquivo vazio: {file_path}")
                return False
        except (OSError, IOError) as e:
            logger.error(f"Erro ao verificar tamanho do arquivo {file_path}: {e}")
            return False

        # Verificar se é um arquivo binário
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                try:
                    f.read(1024)
                except UnicodeDecodeError:
                    logger.warning(f"Arquivo binário: {file_path}")
                    return False
        except (OSError, IOError) as e:
            logger.error(f"Erro ao verificar se o arquivo é binário {file_path}: {e}")
            return False

        return True

    def index_codebase(self, codebase_path: str) -> CodeIndex:
        """
        Indexa um codebase completo.

        Args:
            codebase_path: Caminho para o codebase

        Returns:
            Índice de código
        """
        logger.info(f"Indexando codebase: {codebase_path}")

        # Coletar arquivos a serem indexados
        files_to_index = []
        for root, _, files in os.walk(codebase_path):
            for file in files:
                file_path = os.path.join(root, file)
                if self.should_index_file(file_path):
                    files_to_index.append(file_path)

        logger.info(f"Encontrados {len(files_to_index)} arquivos para indexar")

        # Indexar arquivos
        return self.index_files(files_to_index)

    def index_files(self, file_paths: List[str]) -> CodeIndex:
        """
        Indexa uma lista de arquivos.

        Args:
            file_paths: Lista de caminhos de arquivos

        Returns:
            Índice de código
        """
        # Criar índice
        index = CodeIndex(config={
            'embedding_dim': self.embedding_model.embedding_dim,
            'use_faiss': True
        })

        # Processar arquivos em paralelo
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submeter tarefas
            future_to_file = {
                executor.submit(self._process_file, file_path): file_path
                for file_path in file_paths
            }

            # Processar resultados
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    chunks = future.result()
                    if chunks:
                        for chunk in chunks:
                            # Gerar embedding para o chunk
                            embedding = self.embedding_model.embed_code(chunk.content)

                            # Adicionar ao índice
                            index.add(chunk, embedding)
                except Exception as e:
                    logger.error(f"Erro ao processar arquivo {file_path}: {e}")

        logger.info(f"Indexação concluída. {index.size()} chunks indexados.")

        return index

    def _process_file(self, file_path: str) -> List[CodeChunk]:
        """
        Processa um arquivo para indexação.

        Args:
            file_path: Caminho do arquivo

        Returns:
            Lista de chunks de código
        """
        logger.debug(f"Processando arquivo: {file_path}")

        try:
            # Ler conteúdo do arquivo
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Detectar linguagem
            _, ext = os.path.splitext(file_path.lower())
            language = None
            for lang, exts in {
                'python': ['.py'],
                'javascript': ['.js', '.jsx'],
                'typescript': ['.ts', '.tsx'],
                'java': ['.java'],
                'c': ['.c', '.h'],
                'cpp': ['.cpp', '.hpp'],
                'csharp': ['.cs'],
                'go': ['.go'],
                'ruby': ['.rb'],
                'php': ['.php'],
                'swift': ['.swift'],
                'kotlin': ['.kt'],
                'rust': ['.rs'],
                'scala': ['.scala'],
                'html': ['.html', '.htm'],
                'css': ['.css', '.scss', '.sass'],
                'sql': ['.sql']
            }.items():
                if ext in exts:
                    language = lang
                    break

            # Dividir em chunks
            return self._split_into_chunks(content, file_path, language)
        except Exception as e:
            logger.error(f"Erro ao processar arquivo {file_path}: {e}")
            return []

    def _split_into_chunks(self, content: str, file_path: str, language: Optional[str] = None) -> List[CodeChunk]:
        """
        Divide o conteúdo de um arquivo em chunks.

        Args:
            content: Conteúdo do arquivo
            file_path: Caminho do arquivo
            language: Linguagem de programação

        Returns:
            Lista de chunks de código
        """
        # Dividir em linhas
        lines = content.split('\n')

        # Extrair símbolos importantes
        symbols = self._extract_symbols(content, language)

        # Dividir em chunks
        chunks = []

        # Estratégia 1: Dividir por função/classe
        if language in ['python', 'javascript', 'typescript', 'java', 'csharp']:
            symbol_chunks = self._split_by_symbols(lines, language)
            if symbol_chunks:
                for start, end, chunk_symbols in symbol_chunks:
                    chunk_content = '\n'.join(lines[start:end+1])
                    chunks.append(CodeChunk(
                        content=chunk_content,
                        file_path=file_path,
                        start_line=start + 1,  # 1-based
                        end_line=end + 1,      # 1-based
                        language=language,
                        symbols=chunk_symbols
                    ))
                return chunks

        # Estratégia 2: Dividir por tamanho fixo com sobreposição
        i = 0
        while i < len(lines):
            # Determinar fim do chunk
            end = min(i + self.chunk_size, len(lines))

            # Criar chunk
            chunk_content = '\n'.join(lines[i:end])
            chunks.append(CodeChunk(
                content=chunk_content,
                file_path=file_path,
                start_line=i + 1,      # 1-based
                end_line=end,          # 1-based
                language=language,
                symbols=symbols
            ))

            # Avançar com sobreposição
            i += self.chunk_size - self.chunk_overlap

        return chunks

    def _split_by_symbols(self, lines: List[str], language: str) -> List[Tuple[int, int, List[str]]]:
        """
        Divide o código por símbolos (funções, classes, etc.).

        Args:
            lines: Linhas do arquivo
            language: Linguagem de programação

        Returns:
            Lista de tuplas (linha_inicial, linha_final, símbolos)
        """
        chunks = []

        if language == 'python':
            # Padrões para Python
            class_pattern = re.compile(r'^class\s+(\w+)')
            func_pattern = re.compile(r'^def\s+(\w+)')

            current_start = 0
            current_symbols = []

            for i, line in enumerate(lines):
                line = line.strip()

                # Detectar início de classe ou função
                class_match = class_pattern.match(line)
                func_match = func_pattern.match(line)

                if class_match or func_match:
                    # Se já temos um chunk em andamento, finalizá-lo
                    if current_symbols:
                        chunks.append((current_start, i - 1, current_symbols))

                    # Iniciar novo chunk
                    current_start = i
                    if class_match:
                        current_symbols = [f"class:{class_match.group(1)}"]
                    else:
                        current_symbols = [f"function:{func_match.group(1)}"]

            # Adicionar último chunk
            if current_symbols:
                chunks.append((current_start, len(lines) - 1, current_symbols))

        elif language in ['javascript', 'typescript']:
            # Padrões para JavaScript/TypeScript
            class_pattern = re.compile(r'^class\s+(\w+)')
            func_pattern = re.compile(r'^(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s*)?\([^)]*\)\s*=>|(\w+)\s*:\s*(?:async\s*)?\([^)]*\)\s*=>)')

            current_start = 0
            current_symbols = []

            for i, line in enumerate(lines):
                line = line.strip()

                # Detectar início de classe ou função
                class_match = class_pattern.match(line)
                func_match = func_pattern.match(line)

                if class_match or func_match:
                    # Se já temos um chunk em andamento, finalizá-lo
                    if current_symbols:
                        chunks.append((current_start, i - 1, current_symbols))

                    # Iniciar novo chunk
                    current_start = i
                    if class_match:
                        current_symbols = [f"class:{class_match.group(1)}"]
                    else:
                        func_name = next(filter(None, func_match.groups()), "anonymous")
                        current_symbols = [f"function:{func_name}"]

            # Adicionar último chunk
            if current_symbols:
                chunks.append((current_start, len(lines) - 1, current_symbols))

        return chunks

    def _extract_symbols(self, content: str, language: Optional[str] = None) -> List[str]:
        """
        Extrai símbolos importantes do código.

        Args:
            content: Conteúdo do arquivo
            language: Linguagem de programação

        Returns:
            Lista de símbolos
        """
        symbols = []

        if language == 'python':
            # Extrair classes
            class_pattern = re.compile(r'class\s+(\w+)')
            for match in class_pattern.finditer(content):
                symbols.append(f"class:{match.group(1)}")

            # Extrair funções
            func_pattern = re.compile(r'def\s+(\w+)')
            for match in func_pattern.finditer(content):
                symbols.append(f"function:{match.group(1)}")

        elif language in ['javascript', 'typescript']:
            # Extrair classes
            class_pattern = re.compile(r'class\s+(\w+)')
            for match in class_pattern.finditer(content):
                symbols.append(f"class:{match.group(1)}")

            # Extrair funções
            func_pattern = re.compile(r'function\s+(\w+)')
            for match in func_pattern.finditer(content):
                symbols.append(f"function:{match.group(1)}")

            # Extrair funções arrow
            arrow_func_pattern = re.compile(r'const\s+(\w+)\s*=\s*(?:async\s*)?\([^)]*\)\s*=>')
            for match in arrow_func_pattern.finditer(content):
                symbols.append(f"function:{match.group(1)}")

        return symbols

    def rebuild_indices(force=False):
        from .retrieval import VectorStore
        if force:
            VectorStore.clear_cache()
        VectorStore.initialize()
    
        # Reconstrói todos os índices FAISS
        from .embedding import CodeEmbedder
        embedder = CodeEmbedder()
        embedder.create_faiss_index(force_rebuild=force)

__all__ = ['CodeChunk', 'CodeIndex', 'rebuild_indices']