"""
Módulo de Conversação

Este módulo implementa a estrutura de conversação para o Augment Agent.
"""

import time
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, field

@dataclass
class Message:
    """
    Representa uma mensagem na conversação.
    """
    role: str  # 'user', 'assistant', 'system'
    content: str
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Converte a mensagem para um dicionário.
        
        Returns:
            Dicionário com os dados da mensagem
        """
        return {
            'role': self.role,
            'content': self.content,
            'id': self.id,
            'timestamp': self.timestamp,
            'metadata': self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """
        Cria uma mensagem a partir de um dicionário.
        
        Args:
            data: Dicionário com os dados da mensagem
            
        Returns:
            Mensagem
        """
        return cls(
            role=data['role'],
            content=data['content'],
            id=data.get('id', str(uuid.uuid4())),
            timestamp=data.get('timestamp', time.time()),
            metadata=data.get('metadata', {})
        )
        
    @classmethod
    def system(cls, content: str) -> 'Message':
        """
        Cria uma mensagem do sistema.
        
        Args:
            content: Conteúdo da mensagem
            
        Returns:
            Mensagem do sistema
        """
        return cls(role='system', content=content)
        
    @classmethod
    def user(cls, content: str) -> 'Message':
        """
        Cria uma mensagem do usuário.
        
        Args:
            content: Conteúdo da mensagem
            
        Returns:
            Mensagem do usuário
        """
        return cls(role='user', content=content)
        
    @classmethod
    def assistant(cls, content: str) -> 'Message':
        """
        Cria uma mensagem do assistente.
        
        Args:
            content: Conteúdo da mensagem
            
        Returns:
            Mensagem do assistente
        """
        return cls(role='assistant', content=content)

class Conversation:
    """
    Representa uma conversação entre o usuário e o assistente.
    """
    
    def __init__(self, id: Optional[str] = None, system_message: Optional[str] = None):
        """
        Inicializa uma conversação.
        
        Args:
            id: ID da conversação (opcional)
            system_message: Mensagem do sistema (opcional)
        """
        self.id = id or str(uuid.uuid4())
        self.messages: List[Message] = []
        self.metadata: Dict[str, Any] = {}
        
        # Adicionar mensagem do sistema se fornecida
        if system_message:
            self.add_message(Message.system(system_message))
            
    def add_message(self, message: Message) -> None:
        """
        Adiciona uma mensagem à conversação.
        
        Args:
            message: Mensagem a ser adicionada
        """
        self.messages.append(message)
        
    def add_user_message(self, content: str) -> Message:
        """
        Adiciona uma mensagem do usuário à conversação.
        
        Args:
            content: Conteúdo da mensagem
            
        Returns:
            Mensagem adicionada
        """
        message = Message.user(content)
        self.add_message(message)
        return message
        
    def add_assistant_message(self, content: str) -> Message:
        """
        Adiciona uma mensagem do assistente à conversação.
        
        Args:
            content: Conteúdo da mensagem
            
        Returns:
            Mensagem adicionada
        """
        message = Message.assistant(content)
        self.add_message(message)
        return message
        
    def get_messages(self) -> List[Message]:
        """
        Obtém todas as mensagens da conversação.
        
        Returns:
            Lista de mensagens
        """
        return self.messages.copy()
        
    def get_messages_for_llm(self) -> List[Dict[str, str]]:
        """
        Obtém as mensagens da conversação no formato para LLM.
        
        Returns:
            Lista de mensagens no formato para LLM
        """
        return [{'role': msg.role, 'content': msg.content} for msg in self.messages]
        
    def get_last_message(self) -> Optional[Message]:
        """
        Obtém a última mensagem da conversação.
        
        Returns:
            Última mensagem ou None se não houver mensagens
        """
        if not self.messages:
            return None
            
        return self.messages[-1]
        
    def get_last_user_message(self) -> Optional[Message]:
        """
        Obtém a última mensagem do usuário.
        
        Returns:
            Última mensagem do usuário ou None se não houver mensagens do usuário
        """
        for message in reversed(self.messages):
            if message.role == 'user':
                return message
                
        return None
        
    def get_last_assistant_message(self) -> Optional[Message]:
        """
        Obtém a última mensagem do assistente.
        
        Returns:
            Última mensagem do assistente ou None se não houver mensagens do assistente
        """
        for message in reversed(self.messages):
            if message.role == 'assistant':
                return message
                
        return None
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Converte a conversação para um dicionário.
        
        Returns:
            Dicionário com os dados da conversação
        """
        return {
            'id': self.id,
            'messages': [msg.to_dict() for msg in self.messages],
            'metadata': self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Conversation':
        """
        Cria uma conversação a partir de um dicionário.
        
        Args:
            data: Dicionário com os dados da conversação
            
        Returns:
            Conversação
        """
        conversation = cls(id=data.get('id'))
        conversation.metadata = data.get('metadata', {})
        
        for msg_data in data.get('messages', []):
            conversation.add_message(Message.from_dict(msg_data))
            
        return conversation
