"""
Gema Agent - Interface para interagir com o Gema usando linguagem natural

Este script permite que você dê instruções em linguagem natural para o Gema
e ele execute tarefas de programação automaticamente.
"""

import os
import sys
import json
import re
import argparse

# Adicionar diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar componentes necessários
from context_engine import ContextEngine
from llm_integration import LocalLLM
from agent.learning import LearningSystem
from tools.tool_interface import ToolRegistry
from tools.code_tools import CodeTools
from tools.process_tools import ProcessTools
from tools.search_tools import SearchTools

# Importar ferramentas de codificação universal
from encoding_tools import (
    write_file as write_file_unicode,
    read_file as read_file_unicode,
    encode_base64 as encode_base64_unicode,
    decode_base64 as decode_base64_unicode,
    debug_string
)

class GemaAgent:
    """Agente Gema para processamento de linguagem natural e execução de tarefas."""

    def __init__(self, config_path=None):
        """Inicializa o agente Gema."""
        self.config = self._load_config(config_path)
        self.setup_components()

    def _load_config(self, config_path):
        """Carrega a configuração do arquivo JSON."""
        # Configuração padrão
        config = {
            'llm': {
                'type': 'local',
                'local': {
                    'model_path': 'C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf',
                    'n_ctx': 4096,
                    'n_batch': 512,
                    'n_gpu_layers': 0,
                    'temperature': 0.7,
                    'max_tokens': 1024
                }
            },
            'context_engine': {
                'embedding_dim': 768,
                'model_type': 'code',
                'cache_dir': './context_engine_cache',
                'max_results': 5
            },
            'agent': {
                'max_context_results': 5,
                'max_conversation_history': 10,
                'cache_dir': './agent_cache',
                'learning': {
                    'enabled': True,
                    'max_examples': 100,
                    'similarity_threshold': 0.5,
                    'feedback_threshold': 4,
                    'cache_dir': './learning_cache'
                }
            }
        }

        # Carregar configuração do arquivo se fornecido
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    file_config = json.load(f)

                # Mesclar configurações
                def merge_dicts(d1, d2):
                    for k, v in d2.items():
                        if k in d1 and isinstance(d1[k], dict) and isinstance(v, dict):
                            merge_dicts(d1[k], v)
                        else:
                            d1[k] = v

                merge_dicts(config, file_config)
                print(f"Configuração carregada de {config_path}")
            except Exception as e:
                print(f"Erro ao carregar configuração de {config_path}: {e}")

        return config

    def setup_components(self):
        """Configura os componentes do agente."""
        print("Inicializando componentes do Gema Agent...")

        # Inicializar motor de contexto
        self.context_engine = ContextEngine(self.config['context_engine'])
        print("✓ Motor de contexto inicializado")

        # Inicializar modelo de linguagem
        self.llm = LocalLLM(config=self.config['llm']['local'])
        print("✓ Modelo de linguagem inicializado")

        # Inicializar registro de ferramentas
        self.tool_registry = ToolRegistry()

        # Configurar ferramentas
        from tools.tool_interface import Tool

        # Registrar ferramentas de codificação universal
        self.tool_registry.register(Tool(
            name="write_file",
            description="Escreve conteúdo em um arquivo com suporte completo a Unicode e caracteres especiais",
            function=write_file_unicode
        ))

        self.tool_registry.register(Tool(
            name="read_file",
            description="Lê o conteúdo de um arquivo com detecção automática de codificação",
            function=read_file_unicode
        ))

        # Importar funções para listar e remover arquivos
        from os import listdir, remove
        from os.path import isfile, join

        def list_files(path='.', pattern=None):
            """Lista arquivos em um diretório."""
            try:
                files = [f for f in listdir(path) if isfile(join(path, f))]
                if pattern:
                    import fnmatch
                    files = [f for f in files if fnmatch.fnmatch(f, pattern)]
                return files
            except Exception as e:
                return f"Erro ao listar arquivos: {str(e)}"

        def remove_file(path):
            """Remove um arquivo."""
            try:
                if isfile(path):
                    remove(path)
                    return f"Arquivo removido: {path}"
                else:
                    return f"Arquivo não encontrado: {path}"
            except Exception as e:
                return f"Erro ao remover arquivo: {str(e)}"

        self.tool_registry.register(Tool(
            name="list_files",
            description="Lista arquivos em um diretório",
            function=list_files
        ))

        self.tool_registry.register(Tool(
            name="remove_file",
            description="Remove um arquivo",
            function=remove_file
        ))

        self.tool_registry.register(Tool(
            name="encode_base64",
            description="Codifica texto em base64, preservando caracteres Unicode",
            function=encode_base64_unicode
        ))

        self.tool_registry.register(Tool(
            name="decode_base64",
            description="Decodifica texto de base64 para Unicode",
            function=decode_base64_unicode
        ))

        self.tool_registry.register(Tool(
            name="debug_string",
            description="Retorna informações de debug sobre uma string",
            function=debug_string
        ))

        # Configurar outras ferramentas
        CodeTools(self.tool_registry, self.context_engine)
        ProcessTools(self.tool_registry)
        SearchTools(self.tool_registry)
        print("✓ Ferramentas inicializadas")

        # Inicializar sistema de aprendizado
        self.learning_system = LearningSystem(
            cache_dir=self.config['agent']['learning']['cache_dir'],
            config=self.config['agent']['learning']
        )
        print("✓ Sistema de aprendizado inicializado")

        # Histórico de conversas
        self.conversation_history = []

        print("Gema Agent inicializado com sucesso!")

    def process_message(self, message):
        """Processa uma mensagem do usuário e retorna uma resposta."""
        print(f"\nProcessando mensagem: {message}")

        # Adicionar mensagem ao histórico
        self.conversation_history.append({"role": "user", "content": message})

        # Obter exemplos relevantes do sistema de aprendizado
        examples = self.learning_system.get_relevant_examples(message)
        examples_text = ""
        if examples:
            examples_text = "Exemplos relevantes:\n\n"
            for i, example in enumerate(examples[:3]):
                examples_text += f"Exemplo {i+1}:\nPergunta: {example['query']}\nResposta: {example['response']}\n\n"

        # Obter aprimoramento do prompt do sistema
        system_enhancement = self.learning_system.get_system_prompt_enhancement()

        # Construir prompt para o modelo
        prompt = f"""Você é o Gema, um assistente de programação avançado que pode executar tarefas de programação automaticamente.

{system_enhancement}

{examples_text}

Você tem acesso às seguintes ferramentas:
1. write_file(path, content, encoding='utf-8') - Cria ou sobrescreve um arquivo com suporte completo a Unicode e caracteres especiais
2. read_file(path, encoding=None) - Lê o conteúdo de um arquivo com detecção automática de codificação
3. list_files(path='.', pattern=None) - Lista arquivos em um diretório
4. remove_file(path) - Remove um arquivo
5. encode_base64(text) - Codifica texto em base64, preservando caracteres Unicode
6. decode_base64(base64_text) - Decodifica texto de base64 para Unicode
7. debug_string(text) - Retorna informações de debug sobre uma string (útil para diagnosticar problemas de codificação)
8. search_code(query, language=None) - Busca código relevante
9. analyze_code(code, language=None) - Analisa um snippet de código
10. format_code(code, language=None) - Formata um snippet de código
11. execute_code(code, language, timeout=10) - Executa um snippet de código
12. execute_command(command) - Executa um comando do sistema
13. web_search(query) - Busca informações na web

Para usar uma ferramenta, use a sintaxe: {{tool.nome_da_ferramenta(arg1=valor1, arg2=valor2)}}

Por exemplo: {{tool.write_file(path='exemplo.py', content='print("Olá, mundo!")', encoding='utf-8')}}

Histórico da conversa:
{chr(10).join([f"{msg['role']}: {msg['content']}" for msg in self.conversation_history[-5:]])}

Responda à mensagem do usuário executando as tarefas necessárias usando as ferramentas disponíveis.
Explique o que você está fazendo em cada etapa.

Mensagem do usuário: {message}

Resposta:"""

        # Gerar resposta
        response = self.llm.generate(prompt)

        # Processar chamadas de ferramentas na resposta
        processed_response = self._process_tool_calls(response)

        # Adicionar resposta ao histórico
        self.conversation_history.append({"role": "assistant", "content": processed_response})

        return processed_response

    def _process_tool_calls(self, text):
        """Processa chamadas de ferramentas no texto."""
        # Padrão para chamadas de ferramentas
        pattern = r'\{tool\.(\w+)\(([^)]*)\)\}'

        # Encontrar todas as chamadas de ferramentas
        matches = re.findall(pattern, text)

        # Processar cada chamada
        processed_text = text
        for tool_name, args_str in matches:
            print(f"Encontrada chamada de ferramenta: {tool_name}({args_str})")

            # Obter ferramenta
            tool = self.tool_registry.get(tool_name)

            if not tool:
                print(f"Ferramenta não encontrada: {tool_name}")
                continue

            # Analisar argumentos
            args = {}
            if args_str:
                # Dividir argumentos
                args_parts = args_str.split(',')

                for part in args_parts:
                    if '=' in part:
                        key, value = part.split('=', 1)
                        key = key.strip()
                        value = value.strip()

                        # Remover aspas
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]

                        args[key] = value

            # Executar ferramenta
            try:
                result = tool.execute(**args)
                print(f"Resultado: {result}")

                # Substituir chamada da ferramenta pelo resultado
                tool_call_str = f"{{tool.{tool_name}({args_str})}}"
                processed_text = processed_text.replace(
                    tool_call_str,
                    f"{{tool.{tool_name} result:}}\n```\n{result}\n```"
                )

            except Exception as e:
                print(f"Erro ao executar {tool_name}: {e}")

                # Substituir chamada da ferramenta pelo erro
                tool_call_str = f"{{tool.{tool_name}({args_str})}}"
                processed_text = processed_text.replace(
                    tool_call_str,
                    f"{{tool.{tool_name} error:}}\n```\n{str(e)}\n```"
                )

        return processed_text

    def add_feedback(self, query, response, rating, comment=None):
        """Adiciona feedback ao sistema de aprendizado."""
        return self.learning_system.add_feedback(query, response, rating, comment)

    def get_stats(self):
        """Obtém estatísticas do agente."""
        return {
            "learning_stats": self.learning_system.get_stats(),
            "conversation_history_length": len(self.conversation_history)
        }

def main():
    """Função principal."""
    parser = argparse.ArgumentParser(description='Gema Agent - Interface para interagir com o Gema usando linguagem natural')
    parser.add_argument('--config', type=str, help='Caminho para o arquivo de configuração')
    parser.add_argument('--interactive', '-i', action='store_true', help='Modo interativo')
    parser.add_argument('--message', '-m', type=str, help='Mensagem para processar')

    args = parser.parse_args()

    # Inicializar agente
    agent = GemaAgent(args.config)

    if args.interactive:
        print("\n" + "=" * 50)
        print("Gema Agent - Modo Interativo")
        print("Digite 'sair' para encerrar")
        print("=" * 50 + "\n")

        while True:
            message = input("\nVocê: ")

            if message.lower() in ['sair', 'exit', 'quit']:
                print("\nEncerrando Gema Agent...")
                break

            response = agent.process_message(message)
            print(f"\nGema: {response}")

            # Solicitar feedback
            rating_input = input("\nAvalie a resposta (1-5, ou pressione Enter para pular): ")
            if rating_input.strip() and rating_input.isdigit():
                rating = int(rating_input)
                if 1 <= rating <= 5:
                    comment = input("Comentário (opcional): ")
                    agent.add_feedback(message, response, rating, comment)
                    print("Feedback registrado. Obrigado!")

    elif args.message:
        response = agent.process_message(args.message)
        print(f"\nGema: {response}")

    else:
        parser.print_help()

if __name__ == "__main__":
    main()
